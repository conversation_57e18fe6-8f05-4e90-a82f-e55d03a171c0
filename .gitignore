# === UNIFIED GITIGNORE FOR VANLANG BUDGET PROJECT ===
# G<PERSON>p từ root, frontend và backend .gitignore

# Dependencies
node_modules/
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# Testing
coverage/
/coverage

# Next.js (Frontend)
.next/
/.next/
out/
/out/

# Production builds
build/
/build
dist/
/dist

# Misc
.DS_Store
*.pem
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
logs/
*.log

# Environment variables
.env
.env*.local
.env.development.local
.env.test.local
.env.production.local

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Python
.venv/
__pycache__/
*.py[cod]
*$py.class

# VS Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Cursor
.cursor/

# Temp files
/temp
temp/

# Documentation and configuration files
*.md
*.cursorrules
.cursor/

# Keep these directories (if needed)
!vanlang-budget-BE/
!vanlang-budget-FE/