{"dashboard": "Dashboard", "dashboardDescription": "View system statistics overview", "logout": "Logout", "header": {"title": "System Administration", "userSettings": "User Settings", "profile": "Profile", "settings": "Settings", "darkMode": "Dark Mode", "lightMode": "Light Mode"}, "stats": {"totalUsers": "Total Users", "registeredUsers": "Registered in the system", "totalTransactions": "Total Transactions", "allTimeTransactions": "All-time system transactions", "activeUsers": "Active Users", "last30Days": "Active in the last 30 days", "todayTransactions": "Today's Transactions", "today": "Made today"}, "recentUsers": "Recent Users", "recentUsersDescription": "Recently registered accounts", "recentTransactions": "Recent Transactions", "recentTransactionsDescription": "Latest transactions in the system", "connectBackendForData": "Connect backend to see real data", "users": {"title": "User Management", "description": "Manage user accounts in the system", "userList": "User List", "userListDescription": "List of all registered users in the system", "searchPlaceholder": "Search by name, email...", "name": "Name", "email": "Email", "role": "Role", "verified": "Verified", "created": "Created Date", "lastLogin": "Last Login", "actions": "Actions", "viewDetails": "View Details", "changeRoleTitle": "Change Role", "changeRoleDescription": "Update user's role", "user": "User", "currentRole": "Current Role", "newRole": "New Role", "roleUser": "User", "roleAdmin": "Administrator", "roleSuperadmin": "Super Administrator", "roleUpdateError": "Error updating role", "userDetails": "User Details", "noUsersFound": "No users found", "createUser": "Create New User", "createUserDescription": "Add a new user account to the system", "firstName": "First Name", "lastName": "Last Name", "password": "Password", "createUserSuccess": "User created successfully", "createUserError": "Error creating user", "promoteToAdmin": "Promote to <PERSON><PERSON>", "demoteToUser": "Demote to User", "deactivateUser": "Deactivate Account", "activateUser": "Activate Account", "processing": "Processing...", "refreshList": "Refresh List", "exportCsv": "Export CSV", "showingUsers": "Showing {showing} of {total} users", "allRoles": "All Roles", "allStatus": "All Status", "statusActive": "Active", "statusInactive": "Inactive"}, "notifications": {"title": "Notification Management", "description": "Manage and send notifications to users", "notificationList": "Notification List", "notificationListDescription": "List of all notifications sent in the system", "searchPlaceholder": "Search notifications...", "createNotification": "Create New Notification", "createNotificationDescription": "Create and send a new notification to users", "titleRequired": "Title is required", "messageRequired": "Message is required", "recipientRequired": "Please select at least one recipient", "createSuccess": "Notification created successfully", "createError": "Error creating notification", "notificationTitle": "Notification Title", "notificationMessage": "Notification Message", "notificationType": "Notification Type", "typeInfo": "Information", "typeWarning": "Warning", "typeSuccess": "Success", "typeError": "Error", "recipients": "Recipients", "sendToAll": "Send to all users", "sendToAdmins": "Only send to administrators", "specificUsers": "Send to specific users", "specificUsersDescription": "Enter a list of emails, separated by commas", "sendNotification": "Send Notification", "cancel": "Cancel", "preview": "Preview", "delete": "Delete", "viewDetails": "View Details", "notificationDetails": "Notification Details", "sentTo": "<PERSON><PERSON>", "sentDate": "Sent Date", "sentCount": "Recipient Count", "close": "Close", "confirmDelete": "Confirm Deletion", "deleteWarning": "Are you sure you want to delete this notification?", "deleteSuccess": "Notification deleted successfully", "deleteError": "Error deleting notification", "noNotificationsFound": "No notifications found", "refreshList": "Refresh List"}}