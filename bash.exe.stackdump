Stack trace:
Frame         Function      Args
0007FFFFAF40  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9E40) msys-2.0.dll+0x1FE8E
0007FFFFAF40  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFB218) msys-2.0.dll+0x67F9
0007FFFFAF40  000210046832 (000210286019, 0007FFFFADF8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFAF40  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFAF40  000210068E24 (0007FFFFAF50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFB220  00021006A225 (0007FFFFAF50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFDEF5E0000 ntdll.dll
7FFDED460000 KERNEL32.DLL
7FFDECFD0000 KERNELBASE.dll
7FFDED8B0000 USER32.dll
7FFDEC830000 win32u.dll
000210040000 msys-2.0.dll
7FFDED3A0000 GDI32.dll
7FFDECA50000 gdi32full.dll
7FFDECDA0000 msvcp_win.dll
7FFDEC860000 ucrtbase.dll
7FFDEEA50000 advapi32.dll
7FFDEE290000 msvcrt.dll
7FFDED5F0000 sechost.dll
7FFDEEB10000 RPCRT4.dll
7FFDEBD60000 CRYPTBASE.DLL
7FFDEC9B0000 bcryptPrimitives.dll
7FFDED5B0000 IMM32.DLL
