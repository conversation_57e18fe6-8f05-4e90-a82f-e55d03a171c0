/* Admin styles - Chỉ áp dụng cho khu vực admin */

/* <PERSON><PERSON><PERSON> tất cả các rule CSS của admin trong .admin-layout để tránh ảnh hưởng đến các phần khác của trang */
/* Base styles for admin pages */
.admin-layout {
    min-height: 100vh;
    background-color: #f9fafb;
    display: flex;
}

/* Admin login page specific styles */
.admin-login-layout {
    min-height: 100vh;
    background-color: #f9fafb;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

/* <PERSON><PERSON><PERSON> bảo các rule cho admin-back-button chỉ áp dụng trong phạm vi admin-layout hoặc admin-login-layout */
.admin-login-layout .admin-back-button,
.admin-layout .admin-back-button {
    position: absolute;
    top: 16px;
    left: 16px;
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background-color: transparent;
    color: #6b7280;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: color 0.2s;
    text-decoration: none;
}

.admin-login-layout .admin-back-button:hover,
.admin-layout .admin-back-button:hover {
    color: #4f46e5;
}

.admin-login-layout .admin-back-button svg,
.admin-layout .admin-back-button svg {
    margin-right: 6px;
}

/* ==== Admin Sidebar Styles ==== */
.admin-layout .admin-sidebar {
    width: 280px;
    background-color: #fff;
    border-right: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 30;
    transition: all 0.3s ease;
}

.admin-layout .admin-sidebar.closed {
    width: 70px;
}

.admin-layout .admin-sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.admin-layout .admin-logo {
    font-size: 1.25rem;
    font-weight: 700;
    color: #111827;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.admin-layout .admin-sidebar.closed .admin-logo {
    display: none;
}

.admin-layout .admin-sidebar-toggle {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.admin-layout .admin-sidebar-toggle:hover {
    background-color: #f3f4f6;
}

.admin-layout .admin-sidebar-nav {
    flex: 1;
    overflow-y: auto;
    padding: 1rem 0;
}

.admin-layout .admin-sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.admin-layout .admin-sidebar-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #4b5563;
    text-decoration: none;
    border-radius: 0.25rem;
    margin: 0.25rem;
    transition: all 0.2s ease;
    cursor: pointer;
}

.admin-layout .admin-sidebar-link:hover,
.admin-layout .admin-sidebar-link.active {
    background-color: #f3f4f6;
    color: #4f46e5;
}

.admin-layout .admin-sidebar-link.active {
    background-color: #e0e7ff;
    color: #4338ca;
}

.admin-layout .admin-sidebar-separator {
    margin: 16px 12px 8px 12px;
    position: relative;
    height: 1px;
}

.admin-layout .admin-sidebar-separator::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    width: 100%;
    height: 1px;
    background-color: #e5e7eb;
}

.admin-layout .admin-sidebar-separator-text {
    position: relative;
    display: inline-block;
    background-color: white;
    padding: 0 8px;
    font-size: 11px;
    font-weight: 500;
    color: #9ca3af;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 8px;
    margin-top: 4px;
}

.admin-layout .admin-superadmin-link {
    color: #b91c1c;
    font-weight: 500;
}

.admin-layout .admin-superadmin-link:hover {
    background-color: #fee2e2;
    color: #b91c1c;
}

.admin-layout .admin-superadmin-link.active {
    background-color: #fee2e2;
    color: #b91c1c;
}

.admin-layout .admin-sidebar-label {
    margin-left: 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.admin-layout .admin-sidebar.closed .admin-sidebar-label {
    display: none;
}

.admin-layout .admin-sidebar-footer {
    border-top: 1px solid #e5e7eb;
    padding: 1rem 0;
}

/* ==== Admin Content Styles ==== */
.admin-layout .admin-content {
    flex: 1;
    margin-left: 280px;
    transition: margin 0.3s ease;
}

.admin-layout .admin-content.sidebar-closed {
    margin-left: 70px;
}

.admin-layout .admin-header {
    background-color: #fff;
    border-bottom: 1px solid #e5e7eb;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
}

.admin-layout .admin-mobile-sidebar-toggle {
    display: none;
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
}

.admin-layout .admin-header-actions {
    display: flex;
    align-items: center;
}

.admin-layout .admin-user-menu {
    position: relative;
}

.admin-layout .admin-user-button {
    display: flex;
    align-items: center;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.25rem;
}

.admin-layout .admin-user-button:hover {
    background-color: #f3f4f6;
}

.admin-layout .admin-user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #4f46e5;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    margin-right: 0.5rem;
}

.admin-layout .admin-user-name {
    font-size: 0.875rem;
    font-weight: 500;
    margin-right: 0.5rem;
    max-width: 120px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.admin-layout .admin-user-dropdown {
    position: absolute;
    right: 0;
    top: 100%;
    width: 240px;
    background-color: #fff;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    z-index: 50;
    margin-top: 0.5rem;
    overflow: hidden;
}

.admin-layout .admin-user-info {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.admin-layout .admin-user-details {
    margin-left: 0.5rem;
}

.admin-layout .admin-user-email {
    font-size: 0.875rem;
    font-weight: 500;
    color: #111827;
    margin: 0;
}

.admin-layout .admin-user-role {
    font-size: 12px;
    color: #6b7280;
    display: block;
    margin-top: -2px;
}

.admin-layout .admin-user-status {
    display: flex;
    align-items: center;
    gap: 0.375rem;
}

.admin-layout .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.admin-layout .admin-user-status.active .status-dot {
    background-color: #10b981;
}

.admin-layout .admin-user-status.inactive .status-dot {
    background-color: #ef4444;
}

.admin-layout .status-text {
    font-size: 0.875rem;
}

.admin-layout .admin-user-status.active .status-text {
    color: #10b981;
}

.admin-layout .admin-user-status.inactive .status-text {
    color: #ef4444;
}

.admin-layout .admin-actions-cell {
    position: relative;
}

.admin-layout .admin-actions-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 0.375rem;
    background-color: transparent;
    border: none;
    color: #6b7280;
    cursor: pointer;
}

.admin-layout .admin-actions-button:hover {
    background-color: #f3f4f6;
}

.admin-layout .admin-actions-dropdown {
    position: absolute;
    right: 0;
    top: 100%;
    width: 200px;
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    z-index: 30;
    overflow: hidden;
}

.admin-layout .admin-dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    color: #4b5563;
    cursor: pointer;
}

.admin-layout .admin-dropdown-item:hover {
    background-color: #f3f4f6;
}

.admin-layout .admin-dropdown-item-danger {
    color: #ef4444;
}

.admin-layout .admin-dropdown-item-danger:hover {
    background-color: #fee2e2;
}

.admin-layout .admin-dropdown-divider {
    height: 1px;
    background-color: #e5e7eb;
    margin: 0.25rem 0;
}

.admin-layout .admin-table-loading,
.admin-layout .admin-table-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    color: #6b7280;
}

.admin-layout .admin-loading-spinner {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid #e5e7eb;
    border-top-color: #4f46e5;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.admin-layout .admin-empty-description {
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #9ca3af;
}

.admin-layout .admin-pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem;
    font-size: 0.875rem;
    color: #4b5563;
}

.admin-layout .admin-pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.admin-layout .admin-pagination-button {
    padding: 0.375rem 0.75rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    background-color: white;
    font-size: 0.875rem;
    color: #4b5563;
    cursor: pointer;
}

.admin-layout .admin-pagination-button:hover:not(:disabled) {
    background-color: #f9fafb;
}

.admin-layout .admin-pagination-button:disabled {
    opacity: 0.5;
    cursor: default;
}

.admin-layout .admin-pagination-pages {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.admin-layout .admin-pagination-page {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 0.375rem;
    background-color: white;
    border: 1px solid #e5e7eb;
    font-size: 0.875rem;
    color: #4b5563;
    cursor: pointer;
}

.admin-layout .admin-pagination-page:hover:not(.active) {
    background-color: #f9fafb;
}

.admin-layout .admin-pagination-page.active {
    background-color: #4f46e5;
    color: white;
    border-color: #4f46e5;
}

.admin-layout .admin-select {
    padding: 0.375rem 0.75rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    background-color: white;
    font-size: 0.875rem;
    color: #4b5563;
}

/* ==== Admin Site Content Page Styles ==== */
.admin-layout .admin-site-content-page {
    width: 100%;
}

.admin-layout .admin-site-content-header {
    margin-bottom: 24px;
}

.admin-layout .admin-site-content-container {
    display: grid;
    grid-template-columns: 240px 1fr;
    gap: 24px;
}

@media (max-width: 768px) {
    .admin-layout .admin-site-content-container {
        grid-template-columns: 1fr;
    }
}

.admin-layout .admin-site-content-sidebar {
    background-color: #fff;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    padding: 16px;
    height: fit-content;
}

.admin-layout .admin-site-content-nav-item {
    margin-bottom: 4px;
}

.admin-layout .admin-site-content-nav-button {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.2s;
    text-align: left;
}

.admin-layout .admin-site-content-nav-button span {
    margin-left: 12px;
}

.admin-layout .admin-site-content-nav-item:not(.active) .admin-site-content-nav-button {
    color: #4b5563;
}

.admin-layout .admin-site-content-nav-item:not(.active) .admin-site-content-nav-button:hover {
    background-color: #f3f4f6;
}

.admin-layout .admin-site-content-nav-item.active .admin-site-content-nav-button {
    background-color: #4f46e5;
    color: white;
}

.admin-layout .admin-site-content-main {
    background-color: #fff;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    padding: 24px;
}

.admin-layout .admin-site-content-main-header {
    margin-bottom: 24px;
}

.admin-layout .admin-site-content-main-title {
    font-size: 18px;
    font-weight: 600;
}

.admin-layout .admin-search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 14px;
}

.admin-layout .admin-content-sections {
    margin-top: 16px;
}

.admin-layout .admin-content-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 6px 6px 0 0;
    cursor: pointer;
}

.admin-layout .admin-content-section-header h3 {
    font-size: 16px;
    font-weight: 500;
}

.admin-layout .admin-content-section-body {
    padding: 16px;
    border: 1px solid #e5e7eb;
    border-top: none;
    border-radius: 0 0 6px 6px;
}

.admin-layout .admin-content-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
}

.admin-layout .admin-content-item {
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    overflow: hidden;
    transition: all 0.2s;
}

.admin-layout .admin-content-item:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.admin-layout .admin-content-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.admin-layout .admin-content-item-title {
    font-weight: 500;
    font-size: 14px;
}

.admin-layout .admin-content-item-key {
    display: block;
    color: #6b7280;
    font-size: 12px;
    font-weight: normal;
    margin-top: 2px;
}

.admin-layout .admin-content-item-actions {
    display: flex;
    gap: 8px;
}

.admin-layout .admin-content-item-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 4px;
    color: #6b7280;
    transition: all 0.2s;
}

.admin-layout .admin-content-item-button:hover {
    background-color: #f3f4f6;
    color: #111827;
}

.admin-layout .admin-content-item-button-save {
    width: auto;
    padding: 0 8px;
}

.admin-layout .admin-content-item-button-save span {
    margin-left: 4px;
}

.admin-layout .admin-content-item-body {
    padding: 16px;
}

.admin-layout .admin-content-item-value {
    font-size: 14px;
    color: #4b5563;
}

.admin-layout .admin-content-item-edit {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.admin-layout .admin-content-item-input,
.admin-layout .admin-content-item-textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 14px;
}

.admin-layout .admin-content-item-textarea {
    resize: vertical;
    min-height: 100px;
}

.admin-layout .admin-content-item-edit-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.admin-layout .admin-content-item-image {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #6b7280;
    font-size: 14px;
}

.admin-layout .admin-content-item-image-edit {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.admin-layout .admin-content-item-image-preview {
    max-height: 120px;
    overflow: hidden;
    border-radius: 4px;
}

.admin-layout .admin-content-item-image-preview img {
    max-width: 100%;
    max-height: 120px;
    object-fit: contain;
}

.admin-layout .admin-content-item-footer {
    padding: 12px 16px;
    border-top: 1px solid #e5e7eb;
    background-color: #f9fafb;
}

.admin-layout .admin-content-item-meta {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #6b7280;
}

.admin-layout .admin-loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px 0;
}

.admin-layout .admin-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-top-color: #4f46e5;
    border-radius: 50%;
    animation: spinner 0.8s linear infinite;
    margin-bottom: 16px;
}

@keyframes spinner {
    to {
        transform: rotate(360deg);
    }
}

.admin-layout .admin-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px 0;
    text-align: center;
}

.admin-layout .admin-empty-state p {
    color: #6b7280;
}

.admin-layout .admin-search-results-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 16px;
}

/* Homepage Section Editing */
.admin-layout .admin-homepage-section {
    background-color: white;
    transition: all 0.2s;
}

.admin-layout .admin-homepage-section:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.admin-layout .admin-section-edit-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.admin-layout .admin-section-preview {
    background-color: #f9fafb;
    border-radius: 6px;
    padding: 16px;
    margin-top: 16px;
}

/* Content History */
.admin-layout .content-history-item {
    margin-bottom: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    overflow: hidden;
}

.admin-layout .content-history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.admin-layout .content-history-body {
    padding: 16px;
}

.admin-layout .admin-version-badge {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    border-radius: 9999px;
    font-size: 12px;
    font-weight: 500;
}

.admin-layout .admin-version-badge-current {
    background-color: #e0f2fe;
    color: #0369a1;
}

.admin-layout .admin-version-badge-old {
    background-color: #f3f4f6;
    color: #4b5563;
}

/* Content Approval */
.admin-layout .admin-approval-banner {
    background-color: #fff7ed;
    border: 1px solid #ffedd5;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 24px;
}

.admin-layout .admin-approval-content {
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 16px;
    margin-top: 16px;
}

.admin-layout .admin-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.admin-layout .admin-modal {
    background-color: white;
    border-radius: 8px;
    padding: 24px;
    width: 100%;
    max-width: 500px;
}

.admin-layout .admin-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.admin-layout .admin-modal-body {
    margin-bottom: 16px;
}

.admin-layout .admin-modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

/* Responsive styles */
@media (max-width: 768px) {
    .admin-layout .admin-sidebar {
        transform: translateX(-100%);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .admin-layout .admin-sidebar.open {
        transform: translateX(0);
        width: 250px;
    }

    .admin-layout .admin-content {
        margin-left: 0 !important;
    }

    .admin-layout .admin-mobile-sidebar-toggle {
        display: block;
    }

    .admin-layout .admin-site-content-container {
        grid-template-columns: 1fr;
    }

    .admin-layout .admin-site-content-sidebar {
        display: none;
    }
}

@media (min-width: 768px) and (max-width: 1024px) {
    .admin-layout .admin-content-items {
        grid-template-columns: repeat(1, 1fr);
    }
}

@media (min-width: 1024px) {
    .admin-layout .admin-content-items {
        grid-template-columns: repeat(1, 1fr);
    }
}

/* Custom scrollbar for admin section only */
.admin-layout ::-webkit-scrollbar,
.admin-login-layout ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.admin-layout ::-webkit-scrollbar-track,
.admin-login-layout ::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.admin-layout ::-webkit-scrollbar-thumb,
.admin-login-layout ::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.admin-layout ::-webkit-scrollbar-thumb:hover,
.admin-login-layout ::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Utility classes for admin area */
.admin-layout .text-muted-foreground {
    color: #6b7280;
}

.admin-layout .bg-muted {
    background-color: #f3f4f6;
}

/* Animation for dashboard loading skeletons */
@keyframes pulse {

    0%,
    100% {
        opacity: 0.5;
    }

    50% {
        opacity: 1;
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.admin-layout .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Rounded utility classes */
.admin-layout .rounded {
    border-radius: 0.25rem;
}

.admin-layout .rounded-full {
    border-radius: 9999px;
}

.admin-layout .rounded-lg {
    border-radius: 0.5rem;
}

/* Spacing utilities */
.admin-layout .space-y-0>*+* {
    margin-top: 0;
}

.admin-layout .space-y-1>*+* {
    margin-top: 0.25rem;
}

.admin-layout .space-y-2>*+* {
    margin-top: 0.5rem;
}

.admin-layout .space-y-4>*+* {
    margin-top: 1rem;
}

.admin-layout .space-y-6>*+* {
    margin-top: 1.5rem;
}

.admin-layout .space-x-4>*+* {
    margin-left: 1rem;
}

/* Border utilities */
.admin-layout .border {
    border-width: 1px;
}

.admin-layout .border-gray-200 {
    border-color: #e5e7eb;
}

/* Text sizes */
.admin-layout .text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
}

.admin-layout .text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.admin-layout .text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
}

.admin-layout .text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
}

/* Font weights */
.admin-layout .font-medium {
    font-weight: 500;
}

.admin-layout .font-semibold {
    font-weight: 600;
}

.admin-layout .font-bold {
    font-weight: 700;
}

/* Tracking */
.admin-layout .tracking-tight {
    letter-spacing: -0.025em;
}

/* Grid utilities */
.admin-layout .grid {
    display: grid;
}

.admin-layout .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

.admin-layout .gap-4 {
    gap: 1rem;
}

.admin-layout .gap-2 {
    gap: 0.5rem;
}

/* Background colors */
.admin-layout .bg-blue-500 {
    background-color: #3b82f6;
}

.admin-layout .bg-green-500 {
    background-color: #10b981;
}

.admin-layout .bg-yellow-500 {
    background-color: #f59e0b;
}

.admin-layout .bg-purple-500 {
    background-color: #8b5cf6;
}

.admin-layout .bg-white {
    background-color: white;
}

/* Shadow utilities */
.admin-layout .shadow-sm {
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* Padding */
.admin-layout .p-2 {
    padding: 0.5rem;
}

.admin-layout .p-4 {
    padding: 0.5rem;
}

.admin-layout .p-6 {
    padding: 1.5rem;
}

.admin-layout .pb-2 {
    padding-bottom: 0.5rem;
}

.admin-layout .pt-0 {
    padding-top: 0;
}

.admin-layout .py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
}

/* Margins */
.admin-layout .mt-1 {
    margin-top: 0.25rem;
}

.admin-layout .mt-2 {
    margin-top: 0.5rem;
}

.admin-layout .mt-4 {
    margin-top: 1rem;
}

.admin-layout .mt-6 {
    margin-top: 1.5rem;
}

/* Text colors */
.admin-layout .text-white {
    color: white;
}

/* Display */
.admin-layout .flex {
    display: flex;
}

.admin-layout .flex-row {
    flex-direction: row;
}

.admin-layout .flex-col {
    flex-direction: column;
}

.admin-layout .flex-1 {
    flex: 1;
}

.admin-layout .items-center {
    align-items: center;
}

.admin-layout .justify-between {
    justify-content: space-between;
}

.admin-layout .justify-center {
    justify-content: center;
}

/* Width utilities */
.admin-layout .w-12 {
    width: 3rem;
}

.admin-layout .w-16 {
    width: 4rem;
}

.admin-layout .w-full {
    width: 100%;
}

/* Overflow */
.admin-layout .overflow-x-auto {
    overflow-x: auto;
}

/* Responsive utilities */
@media (min-width: 640px) {
    .admin-layout .sm\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

@media (min-width: 768px) {
    .admin-layout .md\:flex-row {
        flex-direction: row;
    }
}

@media (min-width: 1024px) {
    .admin-layout .lg\:grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }

    .admin-layout .lg\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

/* Các utility classes cuối cùng nên giữ nguyên */
.text-muted-foreground {
    color: #6b7280;
}

.bg-muted {
    background-color: #f3f4f6;
}

/* Animation for dashboard loading skeletons */
@keyframes pulse {

    0%,
    100% {
        opacity: 0.5;
    }

    50% {
        opacity: 1;
    }
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.mr-2 {
    margin-right: 0.5rem;
}

/* Thêm một quy tắc reset CSS cho lớp admin để đảm bảo không có liên kết nào trong trang chính bị ảnh hưởng */
body:not(.admin-layout) a.admin-back-button {
    all: initial;
    text-decoration: inherit;
    color: inherit;
}

/* Admin User Menu Styles */
.admin-user-role {
    font-size: 12px;
    color: #6b7280;
    display: block;
    margin-top: -2px;
}

.admin-user-info {
    display: flex;
    flex-direction: column;
    margin-left: 8px;
    margin-right: 8px;
    text-align: left;
}

.admin-user-dropdown-header {
    padding: 8px 16px;
    border-bottom: 1px solid #e5e7eb;
}

.admin-user-dropdown-name {
    font-weight: 600;
    font-size: 14px;
    color: #1f2937;
}

.admin-user-dropdown-email {
    font-size: 12px;
    color: #6b7280;
    margin: 2px 0;
}

.admin-user-dropdown-role {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 4px;
    margin-top: 4px;
    display: inline-block;
}

.admin-user-dropdown-role.admin {
    background-color: #e0e7ff;
    color: #4338ca;
}

.admin-user-dropdown-role.superadmin {
    background-color: #fee2e2;
    color: #b91c1c;
    font-weight: 600;
}

.admin-user-dropdown-items {
    padding: 8px 0;
}

.admin-user-dropdown-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    width: 100%;
    text-align: left;
    cursor: pointer;
    background: none;
    border: none;
}

.admin-user-dropdown-item:hover {
    background-color: #f3f4f6;
}

.admin-user-dropdown-item svg {
    margin-right: 8px;
}