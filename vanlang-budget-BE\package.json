{"name": "vanlang-budget-backend", "version": "1.0.0", "description": "Backend API for VanLang Budget application with enhanced chatbot", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "test:chatbot": "node test-enhanced-chatbot.js", "test:agent": "node src/agent/test-agent.js"}, "dependencies": {"@google/generative-ai": "^0.2.1", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "chalk": "^5.3.0", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.0.0", "hpp": "^0.2.3", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^7.8.7", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cache": "^5.1.2", "node-cron": "^3.0.2", "node-fetch": "^3.3.2", "node-nlp": "^5.0.0-alpha.5", "nodemailer": "^6.9.4", "redis": "^4.6.11", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "validator": "^13.11.0", "xss-clean": "^0.1.4"}, "devDependencies": {"@types/node": "^20.5.0", "eslint": "^8.47.0", "jest": "^29.6.2", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "keywords": ["budget", "finance", "api", "nodejs", "express", "mongodb", "chatbot", "ai", "nlp"], "author": "VanLang Budget Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}