{"settings": {"languages": ["vi"], "forceNER": true, "nlu": {"log": false}, "tag": "nlp", "threshold": 0.5, "autoLoad": true, "autoSave": true, "modelFileName": "model.nlp", "executeActionsBeforeAnswers": false, "calculateSentiment": true}, "nluManager": {"settings": {"tag": "nlu-manager", "log": false}, "locales": ["vi"], "languageNames": {}, "domainManagers": {"vi": {"settings": {"locale": "vi", "trainByDomain": false, "tag": "domain-manager-vi", "nluByDomain": {"default": {"className": "NeuralNlu", "settings": {}}}, "useStemDict": true}, "stemDict": {"chao,xin": {"intent": "greeting.hello", "domain": "default"}, "ban,chao": {"intent": "greeting.hello", "domain": "default"}, "hello": {"intent": "greeting.hello", "domain": "default"}, "hi": {"intent": "greeting.hello", "domain": "default"}, "ban,co,khong,o,đo": {"intent": "greeting.hello", "domain": "default"}, "chao,vanlangbot": {"intent": "greeting.hello", "domain": "default"}, "chao": {"intent": "greeting.hello", "domain": "default"}, "alo": {"intent": "greeting.hello", "domain": "default"}, "a,lo": {"intent": "greeting.hello", "domain": "default"}, "bot,e": {"intent": "greeting.hello", "domain": "default"}, "biet,tam": {"intent": "greeting.farewell", "domain": "default"}, "bye": {"intent": "greeting.farewell", "domain": "default"}, "goodbye": {"intent": "greeting.farewell", "domain": "default"}, "biet,chao,tam": {"intent": "greeting.farewell", "domain": "default"}, "gap,hen,lai": {"intent": "greeting.farewell", "domain": "default"}, "bot,cam,on": {"intent": "greeting.farewell", "domain": "default"}, "cam,on": {"intent": "greeting.farewell", "domain": "default"}, "thank,you": {"intent": "greeting.farewell", "domain": "default"}, "thanks": {"intent": "greeting.farewell", "domain": "default"}, "ai,ban,la": {"intent": "bot.introduction", "domain": "default"}, "ban,gioi,thieu,ve": {"intent": "bot.introduction", "domain": "default"}, "ban,gi,ten": {"intent": "bot.introduction", "domain": "default"}, "ban,gi,la": {"intent": "bot.introduction", "domain": "default"}, "ban,thong,tin,ve": {"intent": "bot.introduction", "domain": "default"}, "ai,la,vanlangbot": {"intent": "bot.introduction", "domain": "default"}, "ban,gi,lam,đuoc": {"intent": "bot.capabilities", "domain": "default"}, "ban,gi,giup,toi,đuoc": {"intent": "bot.capabilities", "domain": "default"}, "ban,chuc,cua,nang": {"intent": "bot.capabilities", "domain": "default"}, "ban,cua,gi,kha,la,nang": {"intent": "bot.capabilities", "domain": "default"}, "ban,biet,cho,gi,ke,lam,nghe,toi": {"intent": "bot.capabilities", "domain": "default"}, "gio,may,roi": {"intent": "common.time_date", "domain": "default"}, "hom,may,nay,ngay": {"intent": "common.time_date", "domain": "default"}, "bay,gio,la,may": {"intent": "common.time_date", "domain": "default"}, "gian,hien,tai,thoi": {"intent": "common.time_date", "domain": "default"}, "nam,nay,ngay,thang": {"intent": "common.time_date", "domain": "default"}, "chi,cua,tieu,toi": {"intent": "expense.query_summary", "domain": "default"}, "bao,chi,cua,la,nhieu,tieu,toi,tong": {"intent": "expense.query_summary", "domain": "default"}, "chi,tieu,tong,xem": {"intent": "expense.query_summary", "domain": "default"}, "chi,hinh,tieu,tinh": {"intent": "expense.query_summary", "domain": "default"}, "bao,nhieu,tieu,toi,đa": {"intent": "expense.query_summary", "domain": "default"}, "bao,nay,nhieu,roi,thang,tieu": {"intent": "expense.query_summary", "domain": "default"}, "bao,cao,chi,tieu": {"intent": "expense.query_summary", "domain": "default"}, "chi,ke,thong,tieu": {"intent": "expense.query_summary", "domain": "default"}, "chi,tiet,tieu,xem": {"intent": "expense.query_detail", "domain": "default"}, "cac,chi,ke,khoan,liet": {"intent": "expense.query_detail", "domain": "default"}, "gi,nhung,tieu,toi,vao,đa": {"intent": "expense.query_detail", "domain": "default"}, "chi,cu,the,tieu": {"intent": "expense.query_detail", "domain": "default"}, "cac,chi,cho,dich,giao,tiet,tieu,toi,xem": {"intent": "expense.query_detail", "domain": "default"}, "cac,chi,gan,khoan,đay": {"intent": "expense.query_detail", "domain": "default"}, "cua,nhap,thu,toi": {"intent": "income.query_summary", "domain": "default"}, "bao,la,nhap,nhieu,thu,tong": {"intent": "income.query_summary", "domain": "default"}, "nhap,thu,tong,xem": {"intent": "income.query_summary", "domain": "default"}, "hinh,nhap,thu,tinh": {"intent": "income.query_summary", "domain": "default"}, "bao,kiem,nhieu,toi,đuoc": {"intent": "income.query_summary", "domain": "default"}, "bao,nay,nhap,nhieu,thang,thu": {"intent": "income.query_summary", "domain": "default"}, "bao,cao,nhap,thu": {"intent": "income.query_summary", "domain": "default"}, "nhap,sao,thi,thu,tong": {"intent": "income.query_summary", "domain": "default"}, "chi,nhap,thu,tiet,xem": {"intent": "income.query_detail", "domain": "default"}, "cac,ke,khoan,liet,thu": {"intent": "income.query_detail", "domain": "default"}, "cac,cua,nguon,thu,toi": {"intent": "income.query_detail", "domain": "default"}, "cu,nhap,the,thu": {"intent": "income.query_detail", "domain": "default"}, "cac,chi,cho,dich,giao,nhap,thu,tiet,toi,xem": {"intent": "income.query_detail", "domain": "default"}, "cac,gan,khoan,thu,đay": {"intent": "income.query_detail", "domain": "default"}, "cua,khoan,toi,vay": {"intent": "loan.query_summary", "domain": "default"}, "bao,la,nhieu,no,tong": {"intent": "loan.query_summary", "domain": "default"}, "cac,khoan,vay,xem": {"intent": "loan.query_summary", "domain": "default"}, "hinh,no,tinh": {"intent": "loan.query_summary", "domain": "default"}, "bao,nhieu,no,toi,đang": {"intent": "loan.check", "domain": "default"}, "cac,hien,khoan,tai,tong,vay": {"intent": "loan.query_summary", "domain": "default"}, "bao,cao,khoan,vay": {"intent": "loan.query_summary", "domain": "default"}, "chi,khoan,tiet,vay": {"intent": "loan.query_detail", "domain": "default"}, "cac,ke,khoan,liet,no": {"intent": "loan.query_detail", "domain": "default"}, "abc,chi,khoan,tiet,vay": {"intent": "loan.query_detail", "domain": "default"}, "cu,khoan,the,thong,tin,vay,ve": {"intent": "loan.query_detail", "domain": "default"}, "bao,du,hien,la,nhieu,so,tai": {"intent": "balance.query", "domain": "default"}, "cua,du,so,toi": {"intent": "balance.query", "domain": "default"}, "bao,con,khoan,nhieu,tai,tien": {"intent": "balance.query", "domain": "default"}, "du,kiem,so,tra": {"intent": "balance.query", "domain": "default"}, "bao,con,lai,nhieu,toi": {"intent": "balance.query", "domain": "default"}, "balance": {"intent": "balance.query", "domain": "default"}, "bao,con,cua,ngan,nhieu,sach,toi": {"intent": "budget.check", "domain": "default"}, "kiem,nay,ngan,sach,thang,tra": {"intent": "budget.check", "domain": "default"}, "hinh,ngan,sach,tinh": {"intent": "budget.check", "domain": "default"}, "bao,dung,ngan,nhieu,sach,toi,đa": {"intent": "budget.check", "domain": "default"}, "chi,con,han,lai,muc,tieu": {"intent": "budget.check", "domain": "default"}, "cac,cua,khoan,toi,tu,đau": {"intent": "investment.query", "domain": "default"}, "hinh,tinh,tu,đau": {"intent": "investment.query", "domain": "default"}, "danh,muc,tu,xem,đau": {"intent": "investment.query", "domain": "default"}, "gi,toi,tu,đang,đau": {"intent": "investment.query", "domain": "default"}, "kiem,lam,sao,tien,tiet,đe": {"intent": "saving.advice", "domain": "default"}, "kiem,meo,tien,tiet": {"intent": "saving.advice", "domain": "default"}, "hon,kiem,muon,nhieu,tiet,toi": {"intent": "saving.advice", "domain": "default"}, "cach,goi,kiem,tiet,y": {"intent": "saving.advice", "domain": "default"}, "khuyen,kiem,loi,tiet": {"intent": "saving.advice", "domain": "default"}, "hom,nao,nay,the,thoi,tiet": {"intent": "nlu.fallback", "domain": "default"}, "chuyen,cuoi,ke,đi": {"intent": "nlu.fallback", "domain": "default"}, "an,ban,gi,thich": {"intent": "nlu.fallback", "domain": "default"}, "1,bang,cong,may": {"intent": "nlu.fallback", "domain": "default"}, "abc,xyz": {"intent": "nlu.fallback", "domain": "default"}, "luong,nhan,toi,vua": {"intent": "income.add", "domain": "default"}, "moi,nhap,them,thu": {"intent": "income.add", "domain": "default"}, "dung,ghi,nhan,thuong,tien,ung,vao": {"intent": "income.add", "domain": "default"}, "co,khoan,moi,mot,thu,toi": {"intent": "income.add", "domain": "default"}, "chua,co,khoan,khong,nao,tra,vay": {"intent": "loan.check", "domain": "default"}, "bao,con,nhieu,no,toi": {"intent": "loan.check", "domain": "default"}, "cua,kiem,no,toi,tra,voi": {"intent": "loan.check", "domain": "default"}, "bao,cao,chi,cho,tieu,toi,xem": {"intent": "report.view", "domain": "default"}, "chinh,ke,muon,tai,thong,toi,xem": {"intent": "report.view", "domain": "default"}, "bieu,chi,cho,co,khong,nay,thang,the,thu,toi,đo": {"intent": "report.view", "domain": "default"}, "bao,cao,khong,theo,tuan,xem,đuoc": {"intent": "report.view", "domain": "default"}, "chi,co,danh,gi,hien,la,muc,tieu": {"intent": "category.list", "domain": "default"}, "co,danh,khong,moi,muc,tao,the,toi": {"intent": "category.list", "domain": "default"}, "cac,chi,danh,ke,liet,muc,thu": {"intent": "category.list", "domain": "default"}, "chi,loai,muon,phan,tieu,toi": {"intent": "category.list", "domain": "default"}, "bao,bat,chi,lam,sao,thong,tieu": {"intent": "notification.settings", "domain": "default"}, "het,khi,muon,ngan,nhac,sach,sap,toi,đuoc": {"intent": "notification.settings", "domain": "default"}, "bao,bat,canh,chinh,tai": {"intent": "notification.settings", "domain": "default"}, "bao,co,gioi,han,hoat,khong,ngan,qua,sach,thong,đong": {"intent": "notification.settings", "domain": "default"}, "nao,nay,ngan,nhu,sach,thang,the,đat": {"intent": "budget.set", "domain": "default"}, "chi,gioi,han,muon,tieu,toi": {"intent": "budget.set", "domain": "default"}, "lap,moi,ngan,sach,thiet": {"intent": "budget.set", "domain": "default"}, "can,chi,hoach,ke,lap,thang,tieu,toi": {"intent": "budget.set", "domain": "default"}, "chatbot,chua,co,hoat,khong,nhap,thi,toi,đang,đong": {"intent": "auth.require", "domain": "default"}, "bot,can,co,dung,khong,nhap,su,đang,đe": {"intent": "auth.require", "domain": "default"}, "bot,cau,khoan,khong,tai,yeu,đung": {"intent": "auth.require", "domain": "default"}, "a,hoi,khong,ma,nhap,toi,van,đang,đuoc": {"intent": "auth.require", "domain": "default"}, "bot,gi,lam,nay,đuoc": {"intent": "chatbot.scope", "domain": "default"}, "chuc,cua,gi,la,nang,vanlangbot": {"intent": "chatbot.scope", "domain": "default"}, "bot,cho,co,gi,giup,the,toi": {"intent": "chatbot.scope", "domain": "default"}, "bot,chinh,co,hoi,nao,nhu,tai,the,toi,ve": {"intent": "chatbot.scope", "domain": "default"}, "app,co,gi,the,toi,trong,tu,đau": {"intent": "investment.support", "domain": "default"}, "dung,ho,khong,ly,quan,tro,tu,ung,đau": {"intent": "investment.support", "domain": "default"}, "ghi,loi,nhan,nhuan,o,tu,đau": {"intent": "investment.support", "domain": "default"}, "crypto,doi,nao,nhu,the,theo,tu,đau": {"intent": "investment.support", "domain": "default"}, "kiem,mua,muon,tien,tiet,toi,xe": {"intent": "saving.goal", "domain": "default"}, "kiem,muc,o,tiet,tieu,đat,đau": {"intent": "saving.goal", "domain": "default"}, "hoach,ke,kiem,lam,sao,tao,tiet,đe": {"intent": "saving.goal", "domain": "default"}, "co,doi,khong,kiem,qua,the,theo,tiet,trinh": {"intent": "saving.goal", "domain": "default"}, "ghi,moi,muon,nhac,nhap,thang,thu,toi": {"intent": "reminder.setup", "domain": "default"}, "ky,lam,nhac,sao,tao,đinh": {"intent": "reminder.setup", "domain": "default"}, "hang,nhac,nhe,thang,tien,toi,tro,đong": {"intent": "reminder.setup", "domain": "default"}, "co,dung,ho,khong,ky,nhac,tro,ung,đinh": {"intent": "reminder.setup", "domain": "default"}, "an,co,cua,du,khong,lieu,toan,toi": {"intent": "security.privacy", "domain": "default"}, "co,dung,hoa,khong,ma,thong,tin,ung": {"intent": "security.privacy", "domain": "default"}, "co,khoan,khong,tai,the,toi,xoa": {"intent": "security.privacy", "domain": "default"}, "ba,ben,chia,co,du,khong,lieu,se,thu,vanlangbot,voi": {"intent": "security.privacy", "domain": "default"}, "ban,co,khong,toi,yeu": {"intent": "funny.chat", "domain": "default"}, "bot,buon,co,khi,khong,nhieu,tien,tieu,toi": {"intent": "funny.chat", "domain": "default"}, "ban,gi,giup,pha,roi,san,toi,đuoc": {"intent": "funny.chat", "domain": "default"}, "ban,chuyen,co,cuoi,ke,khong,the": {"intent": "funny.chat", "domain": "default"}}, "intentDict": {"greeting.hello": "default", "greeting.farewell": "default", "bot.introduction": "default", "bot.capabilities": "default", "common.time_date": "default", "expense.query_summary": "default", "expense.query_detail": "default", "income.query_summary": "default", "income.query_detail": "default", "loan.query_summary": "default", "loan.query_detail": "default", "balance.query": "default", "budget.check": "default", "investment.query": "default", "saving.advice": "default", "nlu.fallback": "default", "income.add": "default", "loan.check": "default", "report.view": "default", "category.list": "default", "notification.settings": "default", "budget.set": "default", "auth.require": "default", "chatbot.scope": "default", "investment.support": "default", "saving.goal": "default", "reminder.setup": "default", "security.privacy": "default", "funny.chat": "default"}, "sentences": [{"domain": "default", "utterance": "<PERSON><PERSON> ch<PERSON>o", "intent": "greeting.hello"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> b<PERSON>n", "intent": "greeting.hello"}, {"domain": "default", "utterance": "Hello", "intent": "greeting.hello"}, {"domain": "default", "utterance": "Hi", "intent": "greeting.hello"}, {"domain": "default", "utterance": "Bạn có ở đó không?", "intent": "greeting.hello"}, {"domain": "default", "utterance": "Chào VanLangBot", "intent": "greeting.hello"}, {"domain": "default", "utterance": "chao", "intent": "greeting.hello"}, {"domain": "default", "utterance": "alo", "intent": "greeting.hello"}, {"domain": "default", "utterance": "a lô", "intent": "greeting.hello"}, {"domain": "default", "utterance": "ê bot", "intent": "greeting.hello"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON>", "intent": "greeting.farewell"}, {"domain": "default", "utterance": "Bye", "intent": "greeting.farewell"}, {"domain": "default", "utterance": "Goodbye", "intent": "greeting.farewell"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> tạm bi<PERSON>", "intent": "greeting.farewell"}, {"domain": "default", "utterance": "Hẹn gặp lại", "intent": "greeting.farewell"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> bot", "intent": "greeting.farewell"}, {"domain": "default", "utterance": "c<PERSON>m <PERSON>n", "intent": "greeting.farewell"}, {"domain": "default", "utterance": "thank you", "intent": "greeting.farewell"}, {"domain": "default", "utterance": "thanks", "intent": "greeting.farewell"}, {"domain": "default", "utterance": "Bạn là ai", "intent": "bot.introduction"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u về bạn", "intent": "bot.introduction"}, {"domain": "default", "utterance": "Bạn tên gì", "intent": "bot.introduction"}, {"domain": "default", "utterance": "Bạn là gì", "intent": "bot.introduction"}, {"domain": "default", "utterance": "Thông tin về bạn", "intent": "bot.introduction"}, {"domain": "default", "utterance": "VanLangBot là ai", "intent": "bot.introduction"}, {"domain": "default", "utterance": "<PERSON>ạn làm đ<PERSON> gì", "intent": "bot.capabilities"}, {"domain": "default", "utterance": "Bạn gi<PERSON><PERSON> gì đ<PERSON><PERSON> tôi", "intent": "bot.capabilities"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> n<PERSON>ng c<PERSON><PERSON> bạn", "intent": "bot.capabilities"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> năng của bạn là gì", "intent": "bot.capabilities"}, {"domain": "default", "utterance": "<PERSON><PERSON> cho tôi nghe bạn biết làm gì", "intent": "bot.capabilities"}, {"domain": "default", "utterance": "<PERSON><PERSON>y g<PERSON> r<PERSON>i", "intent": "common.time_date"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> nay ngày m<PERSON>y", "intent": "common.time_date"}, {"domain": "default", "utterance": "Bây giờ là mấy giờ", "intent": "common.time_date"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON><PERSON> gian hiện tại", "intent": "common.time_date"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> tháng năm nay", "intent": "common.time_date"}, {"domain": "default", "utterance": "Chi tiêu của tôi", "intent": "expense.query_summary"}, {"domain": "default", "utterance": "Tổng chi tiêu của tôi là bao nhiêu", "intent": "expense.query_summary"}, {"domain": "default", "utterance": "<PERSON><PERSON> tổng chi tiêu", "intent": "expense.query_summary"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> hình chi tiêu", "intent": "expense.query_summary"}, {"domain": "default", "utterance": "Tôi đã tiêu bao nhiêu", "intent": "expense.query_summary"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON><PERSON> này tiêu bao nhiêu rồi", "intent": "expense.query_summary"}, {"domain": "default", "utterance": "Báo cáo chi tiêu", "intent": "expense.query_summary"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON><PERSON> kê chi tiêu", "intent": "expense.query_summary"}, {"domain": "default", "utterance": "<PERSON>em chi tiết chi tiêu", "intent": "expense.query_detail"}, {"domain": "default", "utterance": "Liệ<PERSON> kê các k<PERSON>n chi", "intent": "expense.query_detail"}, {"domain": "default", "utterance": "Tôi đã tiêu vào những gì", "intent": "expense.query_detail"}, {"domain": "default", "utterance": "<PERSON> tiêu cụ thể", "intent": "expense.query_detail"}, {"domain": "default", "utterance": "Cho tôi xem chi tiết các giao dịch chi tiêu", "intent": "expense.query_detail"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON>n chi gần đây", "intent": "expense.query_detail"}, {"domain": "default", "utterance": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> c<PERSON>a tôi", "intent": "income.query_summary"}, {"domain": "default", "utterance": "<PERSON><PERSON>ng thu nhập là bao nhiêu", "intent": "income.query_summary"}, {"domain": "default", "utterance": "<PERSON><PERSON> tổng thu nhập", "intent": "income.query_summary"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> hình thu nhập", "intent": "income.query_summary"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> ki<PERSON>m đ<PERSON><PERSON><PERSON> bao n<PERSON>u", "intent": "income.query_summary"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON><PERSON> này thu nhập bao nhiêu", "intent": "income.query_summary"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> c<PERSON>o thu nhập", "intent": "income.query_summary"}, {"domain": "default", "utterance": "Tổng thu nhập thì sao", "intent": "income.query_summary"}, {"domain": "default", "utterance": "<PERSON>em chi tiết thu nhập", "intent": "income.query_detail"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> kê các k<PERSON>n thu", "intent": "income.query_detail"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> nguồn thu của tôi", "intent": "income.query_detail"}, {"domain": "default", "utterance": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> cụ thể", "intent": "income.query_detail"}, {"domain": "default", "utterance": "Cho tôi xem chi tiết các giao dịch thu nhập", "intent": "income.query_detail"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON>n thu gần đây", "intent": "income.query_detail"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON><PERSON> vay của tôi", "intent": "loan.query_summary"}, {"domain": "default", "utterance": "Tổng nợ là bao nhiêu", "intent": "loan.query_summary"}, {"domain": "default", "utterance": "<PERSON><PERSON> vay", "intent": "loan.query_summary"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> hình nợ", "intent": "loan.query_summary"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> đang nợ bao nhiêu", "intent": "loan.query_summary"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> c<PERSON>c k<PERSON>n vay hiện tại", "intent": "loan.query_summary"}, {"domain": "default", "utterance": "Báo c<PERSON>o <PERSON> vay", "intent": "loan.query_summary"}, {"domain": "default", "utterance": "<PERSON> tiết k<PERSON> vay", "intent": "loan.query_detail"}, {"domain": "default", "utterance": "Liệt kê các k<PERSON>n nợ", "intent": "loan.query_detail"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON><PERSON> vay abc chi tiết", "intent": "loan.query_detail"}, {"domain": "default", "utterance": "Thông tin cụ thể về khoản vay", "intent": "loan.query_detail"}, {"domain": "default", "utterance": "Số dư hiện tại là bao nhiêu", "intent": "balance.query"}, {"domain": "default", "utterance": "Số dư của tôi", "intent": "balance.query"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON>n còn bao nhiêu tiền", "intent": "balance.query"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> tra số dư", "intent": "balance.query"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> còn lại bao nhi<PERSON>u", "intent": "balance.query"}, {"domain": "default", "utterance": "Balance", "intent": "balance.query"}, {"domain": "default", "utterance": "<PERSON><PERSON> sách của tôi còn bao nhiêu?", "intent": "budget.check"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> tra ngân sách tháng này", "intent": "budget.check"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> hình ngân s<PERSON>ch", "intent": "budget.check"}, {"domain": "default", "utterance": "Tôi đã dùng bao nhiêu ngân sách", "intent": "budget.check"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> mức chi tiêu còn lại", "intent": "budget.check"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON>n đầu tư của tôi", "intent": "investment.query"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> hình đầu tư", "intent": "investment.query"}, {"domain": "default", "utterance": "<PERSON><PERSON> danh mục đ<PERSON>u tư", "intent": "investment.query"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> đang đầu tư gì", "intent": "investment.query"}, {"domain": "default", "utterance": "<PERSON><PERSON>m sao để tiết kiệm tiền?", "intent": "saving.advice"}, {"domain": "default", "utterance": "Mẹo tiết kiệm tiền", "intent": "saving.advice"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> muốn tiết kiệm nhi<PERSON>u hơn", "intent": "saving.advice"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> cách tiết kiệm", "intent": "saving.advice"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON><PERSON><PERSON> tiế<PERSON> ki<PERSON>m", "intent": "saving.advice"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON><PERSON> tiết hôm nay thế nào", "intent": "nlu.fallback"}, {"domain": "default", "utterance": "<PERSON><PERSON> chuy<PERSON>n cư<PERSON>i đi", "intent": "nlu.fallback"}, {"domain": "default", "utterance": "Bạn thích ăn gì", "intent": "nlu.fallback"}, {"domain": "default", "utterance": "1 cộng 1 bằng mấy", "intent": "nlu.fallback"}, {"domain": "default", "utterance": "abc xyz", "intent": "nlu.fallback"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> vừa nhận l<PERSON>", "intent": "income.add"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON><PERSON> thu nhập mới", "intent": "income.add"}, {"domain": "default", "utterance": "<PERSON><PERSON> nhận tiền thưởng vào ứng dụng", "intent": "income.add"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> có một khoản thu mới", "intent": "income.add"}, {"domain": "default", "utterance": "Tôi đang nợ bao nhiêu?", "intent": "loan.check"}, {"domain": "default", "utterance": "<PERSON>ó khoản vay nào chưa trả không?", "intent": "loan.check"}, {"domain": "default", "utterance": "Tôi còn bao nhiêu nợ?", "intent": "loan.check"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> tra nợ của tôi với", "intent": "loan.check"}, {"domain": "default", "utterance": "Cho tôi xem báo cáo chi tiêu", "intent": "report.view"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> muốn xem thống kê tài ch<PERSON>h", "intent": "report.view"}, {"domain": "default", "utterance": "Có thể cho tôi biểu đồ thu chi tháng này không?", "intent": "report.view"}, {"domain": "default", "utterance": "<PERSON>em báo cáo theo tuần đ<PERSON><PERSON><PERSON> không?", "intent": "report.view"}, {"domain": "default", "utterance": "Danh mục chi tiêu hiện có là gì?", "intent": "category.list"}, {"domain": "default", "utterance": "Tôi có thể tạo danh mục mới không?", "intent": "category.list"}, {"domain": "default", "utterance": "Liệ<PERSON> kê các danh mục thu chi", "intent": "category.list"}, {"domain": "default", "utterance": "T<PERSON>i muốn phân loại chi tiêu", "intent": "category.list"}, {"domain": "default", "utterance": "<PERSON><PERSON>m sao bật thông báo chi tiêu?", "intent": "notification.settings"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> mu<PERSON><PERSON><PERSON><PERSON> n<PERSON> khi sắp hết ngân sách", "intent": "notification.settings"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> c<PERSON>nh b<PERSON>o tài ch<PERSON>h", "intent": "notification.settings"}, {"domain": "default", "utterance": "Thông báo quá giới hạn ngân sách có hoạt động không?", "intent": "notification.settings"}, {"domain": "default", "utterance": "Đặt ngân sách tháng này như thế nào?", "intent": "budget.set"}, {"domain": "default", "utterance": "T<PERSON>i muốn giới hạn chi tiêu", "intent": "budget.set"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON><PERSON> lập ngân sách mới", "intent": "budget.set"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> c<PERSON>n lập kế hoạch chi tiêu tháng tới", "intent": "budget.set"}, {"domain": "default", "utterance": "Tôi chưa đăng nhập thì chatbot có hoạt động không?", "intent": "auth.require"}, {"domain": "default", "utterance": "<PERSON>ó cần đăng nhập để sử dụng bot không?", "intent": "auth.require"}, {"domain": "default", "utterance": "<PERSON>t yêu cầu tài khoản đúng không?", "intent": "auth.require"}, {"domain": "default", "utterance": "Tôi không đăng nhập mà vẫn hỏi được à?", "intent": "auth.require"}, {"domain": "default", "utterance": "<PERSON>t này làm đ<PERSON> gì?", "intent": "chatbot.scope"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> năng của VanLangBot là gì?", "intent": "chatbot.scope"}, {"domain": "default", "utterance": "Bot có thể giúp gì cho tôi?", "intent": "chatbot.scope"}, {"domain": "default", "utterance": "Tôi có thể hỏi bot về tài chính như thế nào?", "intent": "chatbot.scope"}, {"domain": "default", "utterance": "Tôi có thể đầu tư gì trong app?", "intent": "investment.support"}, {"domain": "default", "utterance": "Ứng dụng hỗ trợ quản lý đầu tư không?", "intent": "investment.support"}, {"domain": "default", "utterance": "<PERSON><PERSON> nhận lợi nhuận đầu tư ở đâu?", "intent": "investment.support"}, {"domain": "default", "utterance": "<PERSON> dõi đầu tư crypto như thế nào?", "intent": "investment.support"}, {"domain": "default", "utterance": "T<PERSON><PERSON> muốn tiết kiệm tiền mua xe", "intent": "saving.goal"}, {"domain": "default", "utterance": "Đặt mục tiêu tiết kiệm ở đâu?", "intent": "saving.goal"}, {"domain": "default", "utterance": "<PERSON>àm sao để tạo kế hoạch tiết kiệm?", "intent": "saving.goal"}, {"domain": "default", "utterance": "Có thể theo dõi quá trình tiết kiệm không?", "intent": "saving.goal"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON> muốn nh<PERSON>c ghi thu nhập mỗi tháng", "intent": "reminder.setup"}, {"domain": "default", "utterance": "<PERSON>àm sao tạo nhắc định kỳ?", "intent": "reminder.setup"}, {"domain": "default", "utterance": "<PERSON><PERSON><PERSON><PERSON> tôi đóng tiền trọ hàng tháng nhé", "intent": "reminder.setup"}, {"domain": "default", "utterance": "Ứng dụng có hỗ trợ nhắc định kỳ không?", "intent": "reminder.setup"}, {"domain": "default", "utterance": "Dữ liệu của tôi có an toàn không?", "intent": "security.privacy"}, {"domain": "default", "utterance": "Ứng dụng có mã hóa thông tin không?", "intent": "security.privacy"}, {"domain": "default", "utterance": "Tôi có thể xoá tài khoản không?", "intent": "security.privacy"}, {"domain": "default", "utterance": "VanLangBot có chia sẻ dữ liệu với bên thứ ba không?", "intent": "security.privacy"}, {"domain": "default", "utterance": "Bạn có yêu tôi không?", "intent": "funny.chat"}, {"domain": "default", "utterance": "<PERSON>t có buồn không khi tôi tiêu nhiều tiền?", "intent": "funny.chat"}, {"domain": "default", "utterance": "Tôi phá sản rồi, bạn gi<PERSON><PERSON> gì đư<PERSON>?", "intent": "funny.chat"}, {"domain": "default", "utterance": "Bạn có thể kể chuyện cười không?", "intent": "funny.chat"}], "domains": {"master_domain": {"settings": {"locale": "vi", "tag": "nlu-vi", "keepStopwords": true, "nonefeatureValue": 1, "nonedeltaMultiplier": 1.2, "spellCheck": false, "spellCheckDistance": 1, "filterZeros": true, "log": true}, "features": {"1": 1, "xin": 1, "chao": 1, "ban": 1, "hello": 1, "hi": 1, "co": 1, "o": 1, "đo": 1, "khong": 1, "vanlangbot": 1, "alo": 1, "a": 1, "lo": 1, "e": 1, "bot": 1, "tam": 1, "biet": 1, "bye": 1, "goodbye": 1, "hen": 1, "gap": 1, "lai": 1, "cam": 1, "on": 1, "thank": 1, "you": 1, "thanks": 1, "la": 1, "ai": 1, "gioi": 1, "thieu": 1, "ve": 1, "ten": 1, "gi": 1, "thong": 1, "tin": 1, "lam": 1, "đuoc": 1, "giup": 1, "toi": 1, "chuc": 1, "nang": 1, "cua": 1, "kha": 1, "ke": 1, "cho": 1, "nghe": 1, "may": 1, "gio": 1, "roi": 1, "hom": 1, "nay": 1, "ngay": 1, "bay": 1, "thoi": 1, "gian": 1, "hien": 1, "tai": 1, "thang": 1, "nam": 1, "chi": 1, "tieu": 1, "tong": 1, "bao": 1, "nhieu": 1, "xem": 1, "tinh": 1, "hinh": 1, "đa": 1, "cao": 1, "tiet": 1, "liet": 1, "cac": 1, "khoan": 1, "vao": 1, "nhung": 1, "cu": 1, "the": 1, "giao": 1, "dich": 1, "gan": 1, "đay": 1, "thu": 1, "nhap": 1, "kiem": 1, "thi": 1, "sao": 1, "nguon": 1, "vay": 1, "no": 1, "đang": 1, "abc": 1, "so": 1, "du": 1, "con": 1, "tien": 1, "tra": 1, "balance": 1, "ngan": 1, "sach": 1, "dung": 1, "han": 1, "muc": 1, "đau": 1, "tu": 1, "danh": 1, "đe": 1, "meo": 1, "muon": 1, "hon": 1, "goi": 1, "y": 1, "cach": 1, "loi": 1, "khuyen": 1, "nao": 1, "chuyen": 1, "cuoi": 1, "đi": 1, "thich": 1, "an": 1, "cong": 1, "bang": 1, "xyz": 1, "vua": 1, "nhan": 1, "luong": 1, "them": 1, "moi": 1, "ghi": 1, "thuong": 1, "ung": 1, "mot": 1, "chua": 1, "voi": 1, "chinh": 1, "bieu": 1, "theo": 1, "tuan": 1, "tao": 1, "phan": 1, "loai": 1, "bat": 1, "nhac": 1, "khi": 1, "sap": 1, "het": 1, "canh": 1, "qua": 1, "hoat": 1, "đong": 1, "đat": 1, "nhu": 1, "thiet": 1, "lap": 1, "can": 1, "hoach": 1, "chatbot": 1, "su": 1, "yeu": 1, "cau": 1, "đung": 1, "ma": 1, "van": 1, "hoi": 1, "trong": 1, "app": 1, "ho": 1, "tro": 1, "quan": 1, "ly": 1, "nhuan": 1, "doi": 1, "crypto": 1, "mua": 1, "xe": 1, "trinh": 1, "đinh": 1, "ky": 1, "hang": 1, "nhe": 1, "lieu": 1, "toan": 1, "hoa": 1, "xoa": 1, "chia": 1, "se": 1, "ben": 1, "ba": 1, "buon": 1, "pha": 1, "san": 1}, "intents": {"greeting.hello": 1, "greeting.farewell": 1, "bot.introduction": 1, "bot.capabilities": 1, "common.time_date": 1, "expense.query_summary": 1, "expense.query_detail": 1, "income.query_summary": 1, "income.query_detail": 1, "loan.query_summary": 1, "loan.query_detail": 1, "balance.query": 1, "budget.check": 1, "investment.query": 1, "saving.advice": 1, "nlu.fallback": 1, "income.add": 1, "loan.check": 1, "report.view": 1, "category.list": 1, "notification.settings": 1, "budget.set": 1, "auth.require": 1, "chatbot.scope": 1, "investment.support": 1, "saving.goal": 1, "reminder.setup": 1, "security.privacy": 1, "funny.chat": 1}, "intentFeatures": {"greeting.hello": {"xin": 1, "chao": 1, "ban": 1, "hello": 1, "hi": 1, "co": 1, "o": 1, "đo": 1, "khong": 1, "vanlangbot": 1, "alo": 1, "a": 1, "lo": 1, "e": 1, "bot": 1}, "greeting.farewell": {"tam": 1, "biet": 1, "bye": 1, "goodbye": 1, "chao": 1, "hen": 1, "gap": 1, "lai": 1, "cam": 1, "on": 1, "bot": 1, "thank": 1, "you": 1, "thanks": 1}, "bot.introduction": {"ban": 1, "la": 1, "ai": 1, "gioi": 1, "thieu": 1, "ve": 1, "ten": 1, "gi": 1, "thong": 1, "tin": 1, "vanlangbot": 1}, "bot.capabilities": {"ban": 1, "lam": 1, "đuoc": 1, "gi": 1, "giup": 1, "toi": 1, "chuc": 1, "nang": 1, "cua": 1, "kha": 1, "la": 1, "ke": 1, "cho": 1, "nghe": 1, "biet": 1}, "common.time_date": {"may": 1, "gio": 1, "roi": 1, "hom": 1, "nay": 1, "ngay": 1, "bay": 1, "la": 1, "thoi": 1, "gian": 1, "hien": 1, "tai": 1, "thang": 1, "nam": 1}, "expense.query_summary": {"chi": 1, "tieu": 1, "cua": 1, "toi": 1, "tong": 1, "la": 1, "bao": 1, "nhieu": 1, "xem": 1, "tinh": 1, "hinh": 1, "đa": 1, "thang": 1, "nay": 1, "roi": 1, "cao": 1, "thong": 1, "ke": 1}, "expense.query_detail": {"xem": 1, "chi": 1, "tiet": 1, "tieu": 1, "liet": 1, "ke": 1, "cac": 1, "khoan": 1, "toi": 1, "đa": 1, "vao": 1, "nhung": 1, "gi": 1, "cu": 1, "the": 1, "cho": 1, "giao": 1, "dich": 1, "gan": 1, "đay": 1}, "income.query_summary": {"thu": 1, "nhap": 1, "cua": 1, "toi": 1, "tong": 1, "la": 1, "bao": 1, "nhieu": 1, "xem": 1, "tinh": 1, "hinh": 1, "kiem": 1, "đuoc": 1, "thang": 1, "nay": 1, "cao": 1, "thi": 1, "sao": 1}, "income.query_detail": {"xem": 1, "chi": 1, "tiet": 1, "thu": 1, "nhap": 1, "liet": 1, "ke": 1, "cac": 1, "khoan": 1, "nguon": 1, "cua": 1, "toi": 1, "cu": 1, "the": 1, "cho": 1, "giao": 1, "dich": 1, "gan": 1, "đay": 1}, "loan.query_summary": {"khoan": 1, "vay": 1, "cua": 1, "toi": 1, "tong": 1, "no": 1, "la": 1, "bao": 1, "nhieu": 1, "xem": 1, "cac": 1, "tinh": 1, "hinh": 1, "đang": 1, "hien": 1, "tai": 1, "cao": 1}, "loan.query_detail": {"chi": 1, "tiet": 1, "khoan": 1, "vay": 1, "liet": 1, "ke": 1, "cac": 1, "no": 1, "abc": 1, "thong": 1, "tin": 1, "cu": 1, "the": 1, "ve": 1}, "balance.query": {"so": 1, "du": 1, "hien": 1, "tai": 1, "la": 1, "bao": 1, "nhieu": 1, "cua": 1, "toi": 1, "khoan": 1, "con": 1, "tien": 1, "kiem": 1, "tra": 1, "lai": 1, "balance": 1}, "budget.check": {"ngan": 1, "sach": 1, "cua": 1, "toi": 1, "con": 1, "bao": 1, "nhieu": 1, "kiem": 1, "tra": 1, "thang": 1, "nay": 1, "tinh": 1, "hinh": 1, "đa": 1, "dung": 1, "han": 1, "muc": 1, "chi": 1, "tieu": 1, "lai": 1}, "investment.query": {"cac": 1, "khoan": 1, "đau": 1, "tu": 1, "cua": 1, "toi": 1, "tinh": 1, "hinh": 1, "xem": 1, "danh": 1, "muc": 1, "đang": 1, "gi": 1}, "saving.advice": {"lam": 1, "sao": 1, "đe": 1, "tiet": 1, "kiem": 1, "tien": 1, "meo": 1, "toi": 1, "muon": 1, "nhieu": 1, "hon": 1, "goi": 1, "y": 1, "cach": 1, "loi": 1, "khuyen": 1}, "nlu.fallback": {"1": 1, "thoi": 1, "tiet": 1, "hom": 1, "nay": 1, "the": 1, "nao": 1, "ke": 1, "chuyen": 1, "cuoi": 1, "đi": 1, "ban": 1, "thich": 1, "an": 1, "gi": 1, "cong": 1, "bang": 1, "may": 1, "abc": 1, "xyz": 1}, "income.add": {"toi": 1, "vua": 1, "nhan": 1, "luong": 1, "them": 1, "thu": 1, "nhap": 1, "moi": 1, "ghi": 1, "tien": 1, "thuong": 1, "vao": 1, "ung": 1, "dung": 1, "co": 1, "mot": 1, "khoan": 1}, "loan.check": {"toi": 1, "đang": 1, "no": 1, "bao": 1, "nhieu": 1, "co": 1, "khoan": 1, "vay": 1, "nao": 1, "chua": 1, "tra": 1, "khong": 1, "con": 1, "kiem": 1, "cua": 1, "voi": 1}, "report.view": {"cho": 1, "toi": 1, "xem": 1, "bao": 1, "cao": 1, "chi": 1, "tieu": 1, "muon": 1, "thong": 1, "ke": 1, "tai": 1, "chinh": 1, "co": 1, "the": 1, "bieu": 1, "đo": 1, "thu": 1, "thang": 1, "nay": 1, "khong": 1, "theo": 1, "tuan": 1, "đuoc": 1}, "category.list": {"danh": 1, "muc": 1, "chi": 1, "tieu": 1, "hien": 1, "co": 1, "la": 1, "gi": 1, "toi": 1, "the": 1, "tao": 1, "moi": 1, "khong": 1, "liet": 1, "ke": 1, "cac": 1, "thu": 1, "muon": 1, "phan": 1, "loai": 1}, "notification.settings": {"lam": 1, "sao": 1, "bat": 1, "thong": 1, "bao": 1, "chi": 1, "tieu": 1, "toi": 1, "muon": 1, "đuoc": 1, "nhac": 1, "khi": 1, "sap": 1, "het": 1, "ngan": 1, "sach": 1, "canh": 1, "tai": 1, "chinh": 1, "qua": 1, "gioi": 1, "han": 1, "co": 1, "hoat": 1, "đong": 1, "khong": 1}, "budget.set": {"đat": 1, "ngan": 1, "sach": 1, "thang": 1, "nay": 1, "nhu": 1, "the": 1, "nao": 1, "toi": 1, "muon": 1, "gioi": 1, "han": 1, "chi": 1, "tieu": 1, "thiet": 1, "lap": 1, "moi": 1, "can": 1, "ke": 1, "hoach": 1}, "auth.require": {"toi": 1, "chua": 1, "đang": 1, "nhap": 1, "thi": 1, "chatbot": 1, "co": 1, "hoat": 1, "đong": 1, "khong": 1, "can": 1, "đe": 1, "su": 1, "dung": 1, "bot": 1, "yeu": 1, "cau": 1, "tai": 1, "khoan": 1, "đung": 1, "ma": 1, "van": 1, "hoi": 1, "đuoc": 1, "a": 1}, "chatbot.scope": {"bot": 1, "nay": 1, "lam": 1, "đuoc": 1, "gi": 1, "chuc": 1, "nang": 1, "cua": 1, "vanlangbot": 1, "la": 1, "co": 1, "the": 1, "giup": 1, "cho": 1, "toi": 1, "hoi": 1, "ve": 1, "tai": 1, "chinh": 1, "nhu": 1, "nao": 1}, "investment.support": {"toi": 1, "co": 1, "the": 1, "đau": 1, "tu": 1, "gi": 1, "trong": 1, "app": 1, "ung": 1, "dung": 1, "ho": 1, "tro": 1, "quan": 1, "ly": 1, "khong": 1, "ghi": 1, "nhan": 1, "loi": 1, "nhuan": 1, "o": 1, "theo": 1, "doi": 1, "crypto": 1, "nhu": 1, "nao": 1}, "saving.goal": {"toi": 1, "muon": 1, "tiet": 1, "kiem": 1, "tien": 1, "mua": 1, "xe": 1, "đat": 1, "muc": 1, "tieu": 1, "o": 1, "đau": 1, "lam": 1, "sao": 1, "đe": 1, "tao": 1, "ke": 1, "hoach": 1, "co": 1, "the": 1, "theo": 1, "doi": 1, "qua": 1, "trinh": 1, "khong": 1}, "reminder.setup": {"toi": 1, "muon": 1, "nhac": 1, "ghi": 1, "thu": 1, "nhap": 1, "moi": 1, "thang": 1, "lam": 1, "sao": 1, "tao": 1, "đinh": 1, "ky": 1, "đong": 1, "tien": 1, "tro": 1, "hang": 1, "nhe": 1, "ung": 1, "dung": 1, "co": 1, "ho": 1, "khong": 1}, "security.privacy": {"du": 1, "lieu": 1, "cua": 1, "toi": 1, "co": 1, "an": 1, "toan": 1, "khong": 1, "ung": 1, "dung": 1, "ma": 1, "hoa": 1, "thong": 1, "tin": 1, "the": 1, "xoa": 1, "tai": 1, "khoan": 1, "vanlangbot": 1, "chia": 1, "se": 1, "voi": 1, "ben": 1, "thu": 1, "ba": 1}, "funny.chat": {"ban": 1, "co": 1, "yeu": 1, "toi": 1, "khong": 1, "bot": 1, "buon": 1, "khi": 1, "tieu": 1, "nhieu": 1, "tien": 1, "pha": 1, "san": 1, "roi": 1, "giup": 1, "gi": 1, "đuoc": 1, "the": 1, "ke": 1, "chuyen": 1, "cuoi": 1}}, "featuresToIntent": {"1": ["nlu.fallback"], "xin": ["greeting.hello"], "chao": ["greeting.hello", "greeting.farewell"], "ban": ["greeting.hello", "bot.introduction", "bot.capabilities", "nlu.fallback", "funny.chat"], "hello": ["greeting.hello"], "hi": ["greeting.hello"], "co": ["greeting.hello", "income.add", "loan.check", "report.view", "category.list", "notification.settings", "auth.require", "chatbot.scope", "investment.support", "saving.goal", "reminder.setup", "security.privacy", "funny.chat"], "o": ["greeting.hello", "investment.support", "saving.goal"], "đo": ["greeting.hello", "report.view"], "khong": ["greeting.hello", "loan.check", "report.view", "category.list", "notification.settings", "auth.require", "investment.support", "saving.goal", "reminder.setup", "security.privacy", "funny.chat"], "vanlangbot": ["greeting.hello", "bot.introduction", "chatbot.scope", "security.privacy"], "alo": ["greeting.hello"], "a": ["greeting.hello", "auth.require"], "lo": ["greeting.hello"], "e": ["greeting.hello"], "bot": ["greeting.hello", "greeting.farewell", "auth.require", "chatbot.scope", "funny.chat"], "tam": ["greeting.farewell"], "biet": ["greeting.farewell", "bot.capabilities"], "bye": ["greeting.farewell"], "goodbye": ["greeting.farewell"], "hen": ["greeting.farewell"], "gap": ["greeting.farewell"], "lai": ["greeting.farewell", "balance.query", "budget.check"], "cam": ["greeting.farewell"], "on": ["greeting.farewell"], "thank": ["greeting.farewell"], "you": ["greeting.farewell"], "thanks": ["greeting.farewell"], "la": ["bot.introduction", "bot.capabilities", "common.time_date", "expense.query_summary", "income.query_summary", "loan.query_summary", "balance.query", "category.list", "chatbot.scope"], "ai": ["bot.introduction"], "gioi": ["bot.introduction", "notification.settings", "budget.set"], "thieu": ["bot.introduction"], "ve": ["bot.introduction", "loan.query_detail", "chatbot.scope"], "ten": ["bot.introduction"], "gi": ["bot.introduction", "bot.capabilities", "expense.query_detail", "investment.query", "nlu.fallback", "category.list", "chatbot.scope", "investment.support", "funny.chat"], "thong": ["bot.introduction", "expense.query_summary", "loan.query_detail", "report.view", "notification.settings", "security.privacy"], "tin": ["bot.introduction", "loan.query_detail", "security.privacy"], "lam": ["bot.capabilities", "saving.advice", "notification.settings", "chatbot.scope", "saving.goal", "reminder.setup"], "đuoc": ["bot.capabilities", "income.query_summary", "report.view", "notification.settings", "auth.require", "chatbot.scope", "funny.chat"], "giup": ["bot.capabilities", "chatbot.scope", "funny.chat"], "toi": ["bot.capabilities", "expense.query_summary", "expense.query_detail", "income.query_summary", "income.query_detail", "loan.query_summary", "balance.query", "budget.check", "investment.query", "saving.advice", "income.add", "loan.check", "report.view", "category.list", "notification.settings", "budget.set", "auth.require", "chatbot.scope", "investment.support", "saving.goal", "reminder.setup", "security.privacy", "funny.chat"], "chuc": ["bot.capabilities", "chatbot.scope"], "nang": ["bot.capabilities", "chatbot.scope"], "cua": ["bot.capabilities", "expense.query_summary", "income.query_summary", "income.query_detail", "loan.query_summary", "balance.query", "budget.check", "investment.query", "loan.check", "chatbot.scope", "security.privacy"], "kha": ["bot.capabilities"], "ke": ["bot.capabilities", "expense.query_summary", "expense.query_detail", "income.query_detail", "loan.query_detail", "nlu.fallback", "report.view", "category.list", "budget.set", "saving.goal", "funny.chat"], "cho": ["bot.capabilities", "expense.query_detail", "income.query_detail", "report.view", "chatbot.scope"], "nghe": ["bot.capabilities"], "may": ["common.time_date", "nlu.fallback"], "gio": ["common.time_date"], "roi": ["common.time_date", "expense.query_summary", "funny.chat"], "hom": ["common.time_date", "nlu.fallback"], "nay": ["common.time_date", "expense.query_summary", "income.query_summary", "budget.check", "nlu.fallback", "report.view", "budget.set", "chatbot.scope"], "ngay": ["common.time_date"], "bay": ["common.time_date"], "thoi": ["common.time_date", "nlu.fallback"], "gian": ["common.time_date"], "hien": ["common.time_date", "loan.query_summary", "balance.query", "category.list"], "tai": ["common.time_date", "loan.query_summary", "balance.query", "report.view", "notification.settings", "auth.require", "chatbot.scope", "security.privacy"], "thang": ["common.time_date", "expense.query_summary", "income.query_summary", "budget.check", "report.view", "budget.set", "reminder.setup"], "nam": ["common.time_date"], "chi": ["expense.query_summary", "expense.query_detail", "income.query_detail", "loan.query_detail", "budget.check", "report.view", "category.list", "notification.settings", "budget.set"], "tieu": ["expense.query_summary", "expense.query_detail", "budget.check", "report.view", "category.list", "notification.settings", "budget.set", "saving.goal", "funny.chat"], "tong": ["expense.query_summary", "income.query_summary", "loan.query_summary"], "bao": ["expense.query_summary", "income.query_summary", "loan.query_summary", "balance.query", "budget.check", "loan.check", "report.view", "notification.settings"], "nhieu": ["expense.query_summary", "income.query_summary", "loan.query_summary", "balance.query", "budget.check", "saving.advice", "loan.check", "funny.chat"], "xem": ["expense.query_summary", "expense.query_detail", "income.query_summary", "income.query_detail", "loan.query_summary", "investment.query", "report.view"], "tinh": ["expense.query_summary", "income.query_summary", "loan.query_summary", "budget.check", "investment.query"], "hinh": ["expense.query_summary", "income.query_summary", "loan.query_summary", "budget.check", "investment.query"], "đa": ["expense.query_summary", "expense.query_detail", "budget.check"], "cao": ["expense.query_summary", "income.query_summary", "loan.query_summary", "report.view"], "tiet": ["expense.query_detail", "income.query_detail", "loan.query_detail", "saving.advice", "nlu.fallback", "saving.goal"], "liet": ["expense.query_detail", "income.query_detail", "loan.query_detail", "category.list"], "cac": ["expense.query_detail", "income.query_detail", "loan.query_summary", "loan.query_detail", "investment.query", "category.list"], "khoan": ["expense.query_detail", "income.query_detail", "loan.query_summary", "loan.query_detail", "balance.query", "investment.query", "income.add", "loan.check", "auth.require", "security.privacy"], "vao": ["expense.query_detail", "income.add"], "nhung": ["expense.query_detail"], "cu": ["expense.query_detail", "income.query_detail", "loan.query_detail"], "the": ["expense.query_detail", "income.query_detail", "loan.query_detail", "nlu.fallback", "report.view", "category.list", "budget.set", "chatbot.scope", "investment.support", "saving.goal", "security.privacy", "funny.chat"], "giao": ["expense.query_detail", "income.query_detail"], "dich": ["expense.query_detail", "income.query_detail"], "gan": ["expense.query_detail", "income.query_detail"], "đay": ["expense.query_detail", "income.query_detail"], "thu": ["income.query_summary", "income.query_detail", "income.add", "report.view", "category.list", "reminder.setup", "security.privacy"], "nhap": ["income.query_summary", "income.query_detail", "income.add", "auth.require", "reminder.setup"], "kiem": ["income.query_summary", "balance.query", "budget.check", "saving.advice", "loan.check", "saving.goal"], "thi": ["income.query_summary", "auth.require"], "sao": ["income.query_summary", "saving.advice", "notification.settings", "saving.goal", "reminder.setup"], "nguon": ["income.query_detail"], "vay": ["loan.query_summary", "loan.query_detail", "loan.check"], "no": ["loan.query_summary", "loan.query_detail", "loan.check"], "đang": ["loan.query_summary", "investment.query", "loan.check", "auth.require"], "abc": ["loan.query_detail", "nlu.fallback"], "so": ["balance.query"], "du": ["balance.query", "security.privacy"], "con": ["balance.query", "budget.check", "loan.check"], "tien": ["balance.query", "saving.advice", "income.add", "saving.goal", "reminder.setup", "funny.chat"], "tra": ["balance.query", "budget.check", "loan.check"], "balance": ["balance.query"], "ngan": ["budget.check", "notification.settings", "budget.set"], "sach": ["budget.check", "notification.settings", "budget.set"], "dung": ["budget.check", "income.add", "auth.require", "investment.support", "reminder.setup", "security.privacy"], "han": ["budget.check", "notification.settings", "budget.set"], "muc": ["budget.check", "investment.query", "category.list", "saving.goal"], "đau": ["investment.query", "investment.support", "saving.goal"], "tu": ["investment.query", "investment.support"], "danh": ["investment.query", "category.list"], "đe": ["saving.advice", "auth.require", "saving.goal"], "meo": ["saving.advice"], "muon": ["saving.advice", "report.view", "category.list", "notification.settings", "budget.set", "saving.goal", "reminder.setup"], "hon": ["saving.advice"], "goi": ["saving.advice"], "y": ["saving.advice"], "cach": ["saving.advice"], "loi": ["saving.advice", "investment.support"], "khuyen": ["saving.advice"], "nao": ["nlu.fallback", "loan.check", "budget.set", "chatbot.scope", "investment.support"], "chuyen": ["nlu.fallback", "funny.chat"], "cuoi": ["nlu.fallback", "funny.chat"], "đi": ["nlu.fallback"], "thich": ["nlu.fallback"], "an": ["nlu.fallback", "security.privacy"], "cong": ["nlu.fallback"], "bang": ["nlu.fallback"], "xyz": ["nlu.fallback"], "vua": ["income.add"], "nhan": ["income.add", "investment.support"], "luong": ["income.add"], "them": ["income.add"], "moi": ["income.add", "category.list", "budget.set", "reminder.setup"], "ghi": ["income.add", "investment.support", "reminder.setup"], "thuong": ["income.add"], "ung": ["income.add", "investment.support", "reminder.setup", "security.privacy"], "mot": ["income.add"], "chua": ["loan.check", "auth.require"], "voi": ["loan.check", "security.privacy"], "chinh": ["report.view", "notification.settings", "chatbot.scope"], "bieu": ["report.view"], "theo": ["report.view", "investment.support", "saving.goal"], "tuan": ["report.view"], "tao": ["category.list", "saving.goal", "reminder.setup"], "phan": ["category.list"], "loai": ["category.list"], "bat": ["notification.settings"], "nhac": ["notification.settings", "reminder.setup"], "khi": ["notification.settings", "funny.chat"], "sap": ["notification.settings"], "het": ["notification.settings"], "canh": ["notification.settings"], "qua": ["notification.settings", "saving.goal"], "hoat": ["notification.settings", "auth.require"], "đong": ["notification.settings", "auth.require", "reminder.setup"], "đat": ["budget.set", "saving.goal"], "nhu": ["budget.set", "chatbot.scope", "investment.support"], "thiet": ["budget.set"], "lap": ["budget.set"], "can": ["budget.set", "auth.require"], "hoach": ["budget.set", "saving.goal"], "chatbot": ["auth.require"], "su": ["auth.require"], "yeu": ["auth.require", "funny.chat"], "cau": ["auth.require"], "đung": ["auth.require"], "ma": ["auth.require", "security.privacy"], "van": ["auth.require"], "hoi": ["auth.require", "chatbot.scope"], "trong": ["investment.support"], "app": ["investment.support"], "ho": ["investment.support", "reminder.setup"], "tro": ["investment.support", "reminder.setup"], "quan": ["investment.support"], "ly": ["investment.support"], "nhuan": ["investment.support"], "doi": ["investment.support", "saving.goal"], "crypto": ["investment.support"], "mua": ["saving.goal"], "xe": ["saving.goal"], "trinh": ["saving.goal"], "đinh": ["reminder.setup"], "ky": ["reminder.setup"], "hang": ["reminder.setup"], "nhe": ["reminder.setup"], "lieu": ["security.privacy"], "toan": ["security.privacy"], "hoa": ["security.privacy"], "xoa": ["security.privacy"], "chia": ["security.privacy"], "se": ["security.privacy"], "ben": ["security.privacy"], "ba": ["security.privacy"], "buon": ["funny.chat"], "pha": ["funny.chat"], "san": ["funny.chat"]}, "neuralNetwork": {"settings": {"locale": "vi", "tag": "nlu-vi", "keepStopwords": true, "nonefeatureValue": 1, "nonedeltaMultiplier": 1.2, "spellCheck": false, "spellCheckDistance": 1, "filterZeros": true}, "features": ["xin", "chao", "ban", "hello", "hi", "co", "o", "đo", "khong", "<PERSON><PERSON><PERSON>", "alo", "a", "lo", "e", "bot", "tam", "biet", "bye", "goodbye", "hen", "gap", "lai", "cam", "on", "thank", "you", "thanks", "la", "ai", "gioi", "thieu", "ve", "ten", "gi", "thong", "tin", "lam", "<PERSON><PERSON><PERSON>", "g<PERSON><PERSON>", "toi", "chuc", "nang", "cua", "kha", "ke", "cho", "nghe", "may", "gio", "roi", "hom", "nay", "ngay", "bay", "thoi", "gian", "hien", "tai", "thang", "nam", "chi", "tieu", "tong", "bao", "nhieu", "xem", "tinh", "hinh", "đa", "cao", "tiet", "liet", "cac", "khoan", "vao", "nhung", "cu", "the", "giao", "dich", "gan", "đay", "thu", "nhap", "kiem", "thi", "sao", "nguon", "vay", "no", "đang", "abc", "so", "du", "con", "tien", "tra", "balance", "ngan", "sach", "dung", "han", "muc", "<PERSON><PERSON>", "tu", "danh", "đe", "meo", "muon", "hon", "goi", "y", "cach", "loi", "khuyen", "nao", "chuyen", "cuoi", "đi", "thich", "an", "1", "cong", "bang", "xyz", "vua", "nhan", "luong", "them", "moi", "ghi", "thuong", "ung", "mot", "chua", "voi", "chinh", "bieu", "theo", "tuan", "tao", "phan", "loai", "bat", "nhac", "khi", "sap", "het", "canh", "qua", "hoat", "đong", "đat", "nhu", "thiet", "lap", "can", "hoach", "chatbot", "su", "yeu", "cau", "đung", "ma", "van", "hoi", "trong", "app", "ho", "tro", "quan", "ly", "nhuan", "doi", "crypto", "mua", "xe", "trinh", "đ<PERSON>h", "ky", "hang", "nhe", "lieu", "toan", "hoa", "xoa", "chia", "se", "ben", "ba", "buon", "pha", "san"], "intents": ["greeting.hello", "greeting.farewell", "bot.introduction", "bot.capabilities", "common.time_date", "expense.query_summary", "expense.query_detail", "income.query_summary", "income.query_detail", "loan.query_summary", "loan.query_detail", "balance.query", "budget.check", "investment.query", "saving.advice", "nlu.fallback", "income.add", "loan.check", "report.view", "category.list", "notification.settings", "budget.set", "auth.require", "chatbot.scope", "investment.support", "saving.goal", "reminder.setup", "security.privacy", "funny.chat"], "perceptrons": [[0.9077855944633484, 9.734171867370605, 0.8595514297485352, 10.406886100769043, 10.390750885009766, 0.605327844619751, 4.019254207611084, 5.367129325866699, -0.4487513303756714, 0.8706515431404114, 10.334717750549316, 5.019802570343018, 5.3881707191467285, 7.491633892059326, 2.893678903579712, -6.762349605560303, -6.782634735107422, -3.8486411571502686, -3.840998649597168, -1.2908867597579956, -1.2908867597579956, -1.2908867597579956, -3.382688283920288, -3.382688283920288, -1.9351768493652344, -1.9351768493652344, -3.8165769577026367, -2.8106155395507812, -2.227810859680176, -1.3115050792694092, -1.179978370666504, -2.33620285987854, -1.453434705734253, -3.398473024368286, -1.3277084827423096, -1.1633663177490234, -0.9222655296325684, -1.1425169706344604, -0.13352499902248383, -3.178323745727539, -1.5195201635360718, -1.5508692264556885, -1.7560465335845947, -0.01567438803613186, -1.3730577230453491, -0.23039841651916504, -0.010142435319721699, -1.9277504682540894, -0.9739554524421692, -0.9682270288467407, -0.2881407141685486, -1.296330451965332, -0.962541937828064, -0.0424259789288044, -0.7255311608314514, -0.7238410115242004, -0.7368055582046509, -1.685153841972351, -0.9565727710723877, -0.6549108624458313, -1.098722219467163, -1.640657901763916, -0.39173784852027893, -1.0473685264587402, -0.5802956819534302, -0.7419317960739136, -1.4204219579696655, -1.4204219579696655, -0.06008870527148247, -0.33125144243240356, -1.3254321813583374, -0.17661738395690918, -0.6360225081443787, -1.729722023010254, -0.07500051707029343, 0, -0.13974064588546753, -1.3096987009048462, 0, 0, -0.20357336103916168, -0.20357336103916168, -1.2949436902999878, -1.7576130628585815, -1.7105462551116943, -0.1455751210451126, -0.4843533933162689, 0, -0.763155996799469, -1.0274627208709717, -1.308077096939087, -1.9294040203094482, -0.43191370368003845, -0.9430747032165527, -0.005890342406928539, -0.7663092017173767, -0.7850462794303894, -3.793670177459717, -0.9132397770881653, -0.9132397770881653, -1.2866419553756714, -0.09004479646682739, -0.8736087679862976, -1.8327476978302002, -1.331650972366333, -0.3509632647037506, -0.897485613822937, -0.21096378564834595, 0, 0, -0.2772360146045685, -0.2772360146045685, -0.2772360146045685, -1.2072993516921997, -0.12754034996032715, -0.37222597002983093, -1.127317190170288, -1.127317190170288, -0.48193520307540894, -0.7135345339775085, -0.7135345339775085, -0.64590984582901, -0.64590984582901, -0.64590984582901, -1.9294040203094482, -0.108513742685318, -1.287744402885437, -0.108513742685318, 0, -0.8478527665138245, -1.159398078918457, -0.07500051707029343, -0.4492632746696472, 0, -0.45475324988365173, -0.5093081593513489, -0.3778870105743408, -0.2101234346628189, -0.12524092197418213, -0.053274087607860565, -0.5877847075462341, 0, 0, -0.3778870105743408, -0.6234569549560547, -0.3736700415611267, 0, 0, -0.3778870105743408, -0.18414171040058136, -0.18237565457820892, -0.18237565457820892, -0.5006463527679443, 0, -0.5954445004463196, -0.5954445004463196, -0.8320469856262207, 0, -0.0577118806540966, -0.8320469856262207, -1.7740055322647095, -0.560553252696991, -0.560553252696991, -0.3747386932373047, -0.3672196567058563, -0.3672196567058563, 0, 0, -0.34528520703315735, -0.34528520703315735, -0.0408191904425621, -0.0408191904425621, -1.0632036924362183, -0.0573461540043354, 0, 0, 0, -0.0573461540043354, -0.6234569549560547, -0.6234569549560547, 0, 0, -0.5093081593513489, 0, -0.004790857899934053, 0, -0.5093081593513489, -0.5093081593513489, -0.5093081593513489, -0.5093081593513489, -0.3736700415611267, 0, 0, 3.865540481313492], [-1.6973835229873657, -2.1783204078674316, -2.8380815982818604, -3.3610565662384033, -3.3480679988861084, -0.4539386034011841, -0.14672625064849854, 0, -0.8143293857574463, -1.7043639421463013, -3.286322593688965, -1.698635220527649, -1.698635220527649, -2.934403419494629, -0.257881224155426, 6.254115104675293, 5.932730197906494, 10.928561210632324, 10.906118392944336, 4.08610200881958, 4.08610200881958, 2.828022003173828, 5.572071075439453, 5.572071075439453, 5.488165855407715, 5.488165855407715, 10.819184303283691, -1.5907354354858398, -1.0007221698760986, -0.38899943232536316, -0.38899943232536316, -0.7525548934936523, -0.33019575476646423, -1.3591201305389404, -0.503524661064148, -0.41702476143836975, -1.1607637405395508, -0.48231521248817444, -0.08466371148824692, -2.3342390060424805, -0.1994365155696869, -0.21637879312038422, -0.866104006767273, -0.004597580526024103, -1.441144585609436, -0.2920561134815216, -0.285944402217865, -2.042036771774292, -1.0131306648254395, -0.8473862409591675, -0.38057294487953186, -1.3621033430099487, -1.0718770027160645, -0.1910487413406372, -0.8298097252845764, -0.8275729417800903, -0.8443883657455444, -1.3529666662216187, -0.7498171329498291, -0.6032108664512634, -1.9452147483825684, -1.8513282537460327, -0.6895118951797485, -1.7626433372497559, -1.1484456062316895, -1.0302932262420654, -1.4437144994735718, -1.4437144994735718, -0.10564228892326355, -0.4057193994522095, -1.1087485551834106, -0.2750008702278137, -0.8169805407524109, -1.4062354564666748, -0.26963043212890625, 0, -0.38177844882011414, -0.6528485417366028, 0, 0, -0.2508322298526764, -0.2508322298526764, -1.3892253637313843, -1.4832854270935059, -1.7441883087158203, -0.13416975736618042, -0.6864757537841797, 0, -0.5273619890213013, -0.8311882615089417, -0.3021934926509857, -1.7812293767929077, -0.7130356431007385, -0.7130356431007385, -1.2549686431884766, -0.6858832240104675, -0.6360412240028381, -3.391535758972168, -0.8802856206893921, -0.8802856206893921, -0.770348846912384, -0.5432818531990051, -0.8346378207206726, -0.8525792360305786, -0.8525792360305786, -0.25653916597366333, -0.3780493438243866, -0.2596314251422882, 0, 0, -0.294971764087677, -0.294971764087677, -0.294971764087677, -0.46714237332344055, -0.3170994222164154, -0.27228474617004395, -0.7376218438148499, -0.7376218438148499, -0.7376218438148499, 0, 0, -0.5346325039863586, -0.5346325039863586, -0.5346325039863586, -1.7812293767929077, -0.280515193939209, -0.7164232134819031, -0.280515193939209, -0.06661289185285568, -0.6735664010047913, -0.4344750642776489, -0.26963043212890625, -0.47975194454193115, 0, -0.015094324015080929, 0, -0.1653245985507965, 0, -0.24897432327270508, 0, -0.4148377776145935, 0, 0, -0.1653245985507965, -0.45523664355278015, 0, 0, 0, -0.1653245985507965, 0, 0, 0, 0, -0.24897432327270508, -0.5948591828346252, -0.5948591828346252, -0.261923223733902, 0, 0, -0.261923223733902, -0.2726808488368988, -0.2726808488368988, -0.2726808488368988, -0.0996573194861412, 0, 0, 0, 0, -0.08123550564050674, -0.08123550564050674, -0.035382360219955444, -0.035382360219955444, -0.14672625064849854, -0.24897432327270508, -0.24897432327270508, 0, 0, 0, -0.45523664355278015, -0.45523664355278015, 0, 0, 0, 0, -0.0996573194861412, 0, 0, 0, 0, 0, 0, 0, 0, 3.26471280180493], [-0.005817983765155077, -4.679238796234131, 4.839297771453857, -0.05689162760972977, -0.05748875066637993, -2.5987977981567383, -0.7908861637115479, -0.7820544242858887, -1.5391767024993896, 3.8701956272125244, -0.022256504744291306, -0.016999248415231705, -0.016999248415231705, -0.0009133063722401857, -0.054987210780382156, 0, -0.43755537271499634, -0.02088126540184021, -0.020176682621240616, -0.01320678647607565, -0.01320678647607565, -0.01320678647607565, -0.014961089007556438, -0.014961089007556438, -0.015365648083388805, -0.015365648083388805, -0.01802315190434456, 6.056385517120361, 4.315037250518799, 2.339418649673462, 2.455362558364868, 4.988941669464111, 6.788060188293457, 3.163022994995117, 2.26725697517395, 2.4640989303588867, -3.435612678527832, -4.23439359664917, -1.2727913856506348, -2.401123285293579, -1.9211071729660034, -5.325075149536133, -5.419552803039551, -3.401952028274536, -0.8197261691093445, -0.43755537271499634, -0.43755537271499634, -2.056931495666504, -1.9179829359054565, -0.1510462611913681, -0.09456698596477509, -0.3136637508869171, -0.26470425724983215, -1.7526241540908813, -0.1280311495065689, -0.1280311495065689, -1.4124715328216553, -0.4792953431606293, -0.17599864304065704, -0.1272311955690384, -1.430300235748291, -1.3956900835037231, -1.432392954826355, -1.8101342916488647, -1.6393027305603027, -0.19306138157844543, -0.2212238311767578, -0.2212238311767578, -0.0015803907299414277, -0.04342702403664589, -0.21566759049892426, -0.018359960988163948, -0.132315993309021, -2.368603229522705, -0.021240729838609695, -0.0015803907299414277, -2.24814772605896, -2.521512031555176, 0, 0, -0.06669879704713821, -0.06669879704713821, -0.567676842212677, -0.5102291107177734, -0.2513596713542938, -0.0017935259966179729, -0.0017935259966179729, 0, -2.2665865421295166, -0.8647019267082214, -0.08334897458553314, -0.1293569952249527, -0.31432273983955383, -0.3247484564781189, 0, -0.09391679614782333, -0.04462349787354469, -0.26602789759635925, -0.15916845202445984, -0.15916845202445984, -0.12112581729888916, -0.07663799822330475, -1.037582516670227, -0.1810774803161621, -0.1810774803161621, -1.037582516670227, 0, -0.06457138806581497, 0, 0, -0.054061342030763626, -0.054061342030763626, -0.054061342030763626, -0.05693087354302406, -0.04674709215760231, -0.039051201194524765, -0.17364831268787384, -0.17364831268787384, 0, -3.841191530227661, -3.841191530227661, 0, 0, 0, -0.1288655698299408, 0, -0.030664434656500816, 0, 0, -0.013829254545271397, -0.030664434656500816, -0.018475046381354332, -0.12112581729888916, 0, 0, -0.006536807864904404, -0.039051201194524765, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.07663799822330475, -0.07663799822330475, -0.07663799822330475, 0, -0.039051201194524765, -0.013829254545271397, -0.013829254545271397, 0, 0, 0, 0, -0.2583143711090088, 0, 0, -0.08224820345640182, 0, -0.039051201194524765, 0, 0, -0.0020282783079892397, -0.0020282783079892397, -0.0020282783079892397, -0.0020282783079892397, -0.0055952598340809345, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.006536807864904404, 0, -0.08224820345640182, 0, -0.006536807864904404, -0.006536807864904404, -0.006536807864904404, -0.006536807864904404, 0, 0, 0, -0.30928131872588205], [0, -3.355623960494995, 6.8819146156311035, 0, 0, -1.705550193786621, -0.1820770800113678, -0.1820770800113678, -1.6553692817687988, -4.198780059814453, 0, -0.1393698751926422, 0, 0, -3.670823812484741, 0, 1.7013204097747803, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.4235341548919678, -0.936370313167572, -0.9612205028533936, -0.9612205028533936, -1.7718898057937622, -3.494511365890503, 0.14577734470367432, -0.8129838109016418, -0.8028154373168945, 4.5547380447387695, 6.235987186431885, 3.9461731910705566, 0.5939192175865173, 1.306660532951355, 5.84436559677124, 3.8160059452056885, 4.537705898284912, 0.8190763592720032, 1.428529143333435, 1.7013204097747803, -0.3352905511856079, -0.20289842784404755, -4.8602681159973145, -0.07467543333768845, -3.7178962230682373, -0.1743229329586029, -0.0806245282292366, -0.1141253337264061, -0.1141253337264061, -0.15426458418369293, -0.1337694525718689, -0.09577783197164536, -0.06230979040265083, -0.858835756778717, -1.2448265552520752, -0.3600684106349945, -1.168604850769043, -1.0990880727767944, -0.12052057683467865, -0.10889532417058945, -0.10889532417058945, -0.3859286904335022, -0.03969259932637215, -0.39750754833221436, -0.14509283006191254, -0.4514254331588745, -0.5480166673660278, -0.2779196500778198, -0.2779196500778198, -0.0061200689524412155, -0.6538957953453064, -0.020633691921830177, -0.020633691921830177, -0.0003395693493075669, -0.0003395693493075669, -0.5686686635017395, -0.49570196866989136, -1.019071340560913, 0, -0.4529705345630646, -0.14488893747329712, -0.31943950057029724, -0.11076615005731583, -0.5813994407653809, 0, -0.4339090585708618, -0.4339090585708618, -0.004441823344677687, -0.18471582233905792, -0.0466233491897583, -0.03750534728169441, -0.4841417372226715, -0.4841417372226715, 0, 0, -0.031502384692430496, -0.432100385427475, -0.432100385427475, -0.031502384692430496, -0.35551896691322327, -0.00035511632449924946, -0.44879236817359924, 0, 0, 0, 0, 0, 0, 0, -0.49493420124053955, -0.49493420124053955, -0.060558535158634186, -2.4745049476623535, -2.4745049476623535, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.020973050966858864, -0.0011661449680104852, 0, -0.03969259932637215, -0.03969259932637215, -0.2312469780445099, 0, 0, 0, -0.5334577560424805, -0.446460098028183, -0.446460098028183, -0.446460098028183, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.13378675282001495, 0, 0, -0.8195133805274963, 0, 0, -0.1393698751926422, -0.1393698751926422, -0.1393698751926422, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.0815364271402359, -0.0815364271402359, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.697235584259033, -4.697235584259033, -3.5420056617981905], [-0.11220976710319519, -0.3594314455986023, -0.5111085176467896, -0.31524160504341125, -0.31237444281578064, -0.13319510221481323, -0.014093386940658092, -0.010252953507006168, -0.2098027616739273, -0.38631367683410645, -0.30974820256233215, -0.17949436604976654, -0.17949436604976654, -0.014261794276535511, -0.6106604933738708, -0.1780402511358261, -0.1780402511358261, -0.30880969762802124, -0.3084031939506531, -0.12127149850130081, -0.12127149850130081, -0.12127149850130081, -0.16885113716125488, -0.16885113716125488, -0.1670268476009369, -0.1670268476009369, -0.30757391452789307, 0.3392828702926636, -0.527379035949707, 0, 0, -0.03860202431678772, 0, -0.8399648070335388, -0.2857571840286255, -0.04689677804708481, -0.5082229375839233, -0.4465133547782898, -0.0249734316021204, -1.0135563611984253, -0.05674784630537033, -0.05674784630537033, -0.5396476984024048, 0, -0.5433961153030396, -0.002450875472277403, 0, 5.692145824432373, 5.698233604431152, 2.5773181915283203, 0.2697190046310425, 1.8381398916244507, 6.179202079772949, 2.2507383823394775, 3.247929811477661, 5.047870635986328, 3.043769359588623, 2.637070417404175, 1.8461858034133911, 4.109613418579102, -0.9274469614028931, -1.710737943649292, -1.6134413480758667, -2.497945547103882, -2.360297679901123, -0.6220133304595947, -0.48378971219062805, -0.48378971219062805, -0.07709864526987076, -0.006818313151597977, -1.978833556175232, -0.12340077757835388, -1.439099669456482, -1.693496823310852, -0.09094235301017761, -0.014454795978963375, -0.07725446671247482, -2.046602964401245, 0, 0, -0.09061295539140701, -0.09061295539140701, -0.7464228272438049, -0.6608062386512756, -1.095072627067566, -0.007032394874840975, -0.0969986841082573, 0, -1.2525418996810913, -0.2346116453409195, -0.015556220896542072, -0.2184341996908188, -0.9205517768859863, -0.9205517768859863, 0, -0.3371853828430176, -0.918961226940155, -0.45588627457618713, -0.9954841136932373, -0.9954841136932373, -0.10310596972703934, 0, -0.21252869069576263, -0.21167713403701782, -0.21167713403701782, -0.21252869069576263, -0.042753513902425766, -0.03262896090745926, -0.20139819383621216, 0, -0.02624139003455639, -0.02624139003455639, -0.02624139003455639, -0.02998637594282627, -0.018550682812929153, -1.9531469345092773, -0.1316029578447342, -0.1316029578447342, -0.1316029578447342, -0.08211135119199753, -0.08211135119199753, -2.05073618888855, -2.05073618888855, -2.05073618888855, -0.2184341996908188, -0.006641536485403776, -0.09235349297523499, -0.006641536485403776, 0, 0, -0.07931606471538544, -0.06383960694074631, -0.10310596972703934, 0, 0, 0, -0.3260449767112732, -0.002450875472277403, 0, 0, -0.024411777034401894, 0, 0, -0.12262833118438721, -0.20064380764961243, 0, 0, 0, -0.12262833118438721, 0, 0, -0.1440390795469284, -0.10722114145755768, -0.10722114145755768, 0, 0, 0, 0, 0, 0, -0.1605573147535324, -0.1605573147535324, -0.1605573147535324, -0.0014350066194310784, 0, 0, 0, 0, -0.017653098329901695, -0.17187555134296417, -0.00187315559014678, -0.00187315559014678, -0.0053324270993471146, 0, 0, 0, 0, 0, -0.04745236411690712, -0.04745236411690712, -0.1440390795469284, -0.1440390795469284, 0, 0, -0.0014350066194310784, 0, 0, 0, 0, 0, 0, -0.0249734316021204, -0.0249734316021204, 0.3090798828577206], [0, 0, -1.2718429565429688, 0, 0, -1.6972582340240479, 0, 0, -1.1483397483825684, 0, 0, 0, 0, 0, -1.1483397483825684, 0, 0, 0, 0, 0, 0, -2.2728161811828613, 0, 0, 0, 0, 0, -6.663823127746582, 0, -1.2559046745300293, 0, 0, 0, -4.349400520324707, 0.8995000720024109, 0, -4.331655979156494, -1.056102991104126, 0, -3.474616050720215, -1.2718429565429688, -1.2718429565429688, 5.530605316162109, 0, 1.5616689920425415, -7.111423969268799, 0, -0.23293763399124146, -0.23293763399124146, 2.147278070449829, 0, 1.8510347604751587, 0, 0, 0, 0, -0.7838913798332214, -0.25270596146583557, -1.0642107725143433, 0, 2.493086338043213, 10.88911247253418, 5.363925933837891, 0.35613328218460083, 1.7147772312164307, -3.2390308380126953, 1.299869179725647, 1.299869179725647, 6.171685218811035, 1.82613205909729, -8.44041633605957, -0.6059072613716125, -1.847532868385315, -1.5841047763824463, -3.8004822731018066, -3.8004822731018066, -5.876197338104248, -5.876197338104248, -0.8765047788619995, -0.8765047788619995, -0.07008742541074753, -0.07008742541074753, -2.4290688037872314, -2.4290688037872314, -1.056102991104126, 0, -4.331655979156494, 0, -0.659930408000946, -3.3576457500457764, -0.26843634247779846, 0, -0.967218279838562, -0.967218279838562, -3.446658134460449, -1.1780399084091187, 0, 0, -1.5284229516983032, -1.5284229516983032, -0.09743177890777588, -3.393533945083618, -2.6834568977355957, -1.3611971139907837, -1.3611971139907837, -0.5125888586044312, 0, 0, -3.601184368133545, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.3416035175323486, -2.3416035175323486, -4.331655979156494, 0, -1.1483397483825684, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.91465425491333, -2.91465425491333, -2.91465425491333, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.1483397483825684, 0, 0, -1.6590398948547735], [0, 0, -0.3672493100166321, 0, 0, -1.8784862756729126, -0.039689239114522934, 0, -0.41725435853004456, 0, 0, 0, 0, 0, -0.08007344603538513, 0, 0, 0, 0, 0, 0, -1.195439338684082, 0, 0, 0, 0, 0, -1.4285310506820679, 0, -0.5255261659622192, 0, -0.14067281782627106, -0.0933690145611763, 3.1367082595825195, -5.061920642852783, -0.14067281782627106, -0.6156924962997437, -0.20790426433086395, -0.08007344603538513, -3.27280855178833, 0, 0, -2.8695201873779297, 0, -0.36783120036125183, -1.8877683877944946, 0, 0, 0, 0, -1.273632526397705, -1.3414487838745117, 0, 0, -1.273632526397705, 0, -1.5666775703430176, -0.2412446290254593, -1.1500028371810913, 0, 6.383914947509766, 2.5320823192596436, -8.262161254882812, -3.4068710803985596, -0.5414167046546936, 2.9476699829101562, -2.8137123584747314, -2.8137123584747314, 5.097001075744629, -2.1892588138580322, 5.104918956756592, 5.123294353485107, 5.769017219543457, -0.010771257802844048, 4.773599147796631, 5.171988487243652, 5.201339244842529, 3.326012134552002, -0.03743968904018402, -0.03743968904018402, 2.6394925117492676, 2.6394925117492676, -8.59488582611084, -4.069047927856445, -1.320061445236206, -0.011047185398638248, -0.6377005577087402, -0.2472221553325653, -8.083086013793945, -7.083784580230713, -0.1838790476322174, -0.9978299736976624, -0.0013542607193812728, -0.0013542607193812728, -1.195439338684082, -0.49316543340682983, -0.0013542607193812728, -0.016624553129076958, -0.10952237993478775, -0.10952237993478775, -0.4476156532764435, -1.874267578125, -3.229753255844116, -0.4322243630886078, -0.35958531498908997, -1.8224234580993652, 0, -0.09477739781141281, -1.899368166923523, 0, -0.19544842839241028, -0.19544842839241028, -0.19544842839241028, -0.28686943650245667, -0.28686943650245667, -1.328721046447754, -0.09398828446865082, -0.09398828446865082, -0.06912137567996979, -0.239865243434906, -0.239865243434906, 0, 0, 0, 0, -0.0020339961629360914, -0.3762243688106537, -0.0020339961629360914, 0, 0, -0.3720485270023346, -0.3720485270023346, -0.3720485270023346, 0, 0, 0, 0, 0, -0.4033132493495941, 0, 0, -1.2869848012924194, -1.2869848012924194, -0.6156924962997437, 0, 0, 0, 0, 0, -0.39952462911605835, 0, 0, -0.088957279920578, -0.04506611078977585, 0, -1.0811727046966553, -1.0811727046966553, -1.0811727046966553, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.4033132493495941, -0.0018943084869533777, 0, 0, -0.39952462911605835, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -3.209279633959474], [0, 0, -1.1053990125656128, 0, 0, -0.41946518421173096, 0, 0, -3.250371217727661, 0, 0, -1.2997380495071411, 0, 0, -0.5581138134002686, 0, 0, 0, 0, 0, 0, -1.3135349750518799, 0, 0, 0, 0, 0, -3.6430065631866455, 0, 0, 0, 0, 0, -1.5367953777313232, 0, 0, -0.4747280776500702, 4.155209541320801, -1.0586731433868408, 1.9431694746017456, 0, 0, 2.1790926456451416, 0, -0.5723616480827332, -0.4696425497531891, 0, 0, 0, -0.4825872480869293, 0, 0.6477242112159729, 0, 0, 0, 0, -0.6469852328300476, -0.9080816507339478, -0.6428905129432678, 0, -5.571014881134033, -2.412921905517578, 3.7380568981170654, 2.8668534755706787, 1.2916158437728882, 0.3313692510128021, 2.0921902656555176, 2.0921902656555176, -1.2998046875, 1.2521986961364746, -4.853953838348389, -0.5723616480827332, -4.308592796325684, -1.4764446020126343, 0, 0, -5.054788112640381, -5.054788112640381, -0.4696425497531891, -0.4696425497531891, -0.06467234343290329, -0.06467234343290329, 5.334917068481445, 7.154933452606201, 6.350172996520996, 0.047991957515478134, 0.387037068605423, -2.762040615081787, -0.408125102519989, -5.134510517120361, -2.426114559173584, 0, -1.1198850870132446, -1.1198850870132446, -2.5557637214660645, -0.1676587164402008, -1.8003876209259033, 0, -1.6748608350753784, -1.6748608350753784, -0.4053247570991516, 0, 0, -1.116032361984253, -1.116032361984253, 0, -0.08459573239088058, 0, -2.044323682785034, -0.32215461134910583, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.194836139678955, -5.917701721191406, -1.7219822406768799, 0, 0, 0, -0.28501221537590027, -1.1433603763580322, 0, 0, -1.4795619249343872, -1.4795619249343872, 0, 0, 0, 0, -1.7219822406768799, 0, 0, 0, 0, 0, -0.28501221537590027, -0.28501221537590027, 0, 0, 0, 0, -0.08459573239088058, 0, -0.28501221537590027, -0.08459573239088058, 0, 0, 0, -1.2997380495071411, -1.2997380495071411, -1.2997380495071411, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.1129383072257042, -0.1129383072257042, -2.379646690527837], [0, 0, -0.011865543201565742, 0, 0, -2.081338405609131, 0, -0.6458504796028137, -1.4724899530410767, -0.49380066990852356, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.1616424322128296, 0, 0, 0, 0, 0, -0.5621514320373535, 0, 0, 0, -0.43220797181129456, 0, -0.033851079642772675, -0.47547847032546997, -0.43220797181129456, -0.28001669049263, 0, 0, -4.056908130645752, 0, 0, -1.6253610849380493, 0, 0.15802371501922607, -0.7527920603752136, 0, -0.012914149090647697, 0, 0, -1.3517887592315674, -2.205976724624634, 0, 0, -1.3517887592315674, 0, -0.3449496924877167, -0.47781097888946533, -1.357606291770935, 0, -1.7800662517547607, -3.9712913036346436, -8.021064758300781, -3.3825554847717285, -1.0380116701126099, 2.393263578414917, -2.865731954574585, -2.865731954574585, -0.0043237279169261456, -2.333409547805786, 7.770596981048584, 0.4338263273239136, 4.761459827423096, 1.0936838388442993, 0, 0, 5.412916660308838, 3.083517074584961, 0.10112951695919037, 0.10112951695919037, 0.29657575488090515, 0.29657575488090515, 12.37236213684082, -2.006781816482544, -2.0401554107666016, -0.22284731268882751, -0.5287002921104431, 7.385787010192871, -3.7670695781707764, -2.5429155826568604, -0.16558405756950378, -0.07143109291791916, -0.2372877150774002, -0.7753287553787231, -0.2582862079143524, -0.657052755355835, -0.06183292716741562, -0.06753989309072495, -0.16093382239341736, -0.16093382239341736, -0.0043237279169261456, -0.07107970118522644, -5.720653533935547, -0.82371985912323, -0.82371985912323, -5.619962692260742, -0.28001669049263, -0.48449718952178955, -0.6066104769706726, -0.03720561042428017, -0.3822430372238159, -0.3822430372238159, -0.3822430372238159, -0.5701534748077393, -0.5701534748077393, -1.3517887592315674, -0.08173797279596329, -0.08173797279596329, -0.08173797279596329, -0.011865543201565742, -0.011865543201565742, -0.012914149090647697, -0.012914149090647697, -0.012914149090647697, -0.006298998836427927, -0.02012275718152523, -0.02012275718152523, -0.02012275718152523, -2.6479833126068115, -3.8480308055877686, -0.5004963278770447, 0, 0, -0.4749666154384613, 0, -0.49380066990852356, -0.030198073014616966, -0.6458504796028137, -0.16881564259529114, 0, -0.10746132582426071, 0, 0, 0, -0.5004963278770447, 0, 0, 0, 0, -0.16881564259529114, 0, 0, 0, 0, 0, 0, 0, -0.10746132582426071, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.16881564259529114, 0, 0, 0, -0.16881564259529114, 0, 0, 0, 0, -0.49380066990852356, 0, 0, 0, -0.49380066990852356, -0.49380066990852356, -0.49380066990852356, -0.49380066990852356, 0, 0, 0, -4.585812405762661], [0, 0, -1.0619381666183472, 0, 0, -1.6337233781814575, 0, 0, -2.5530104637145996, -1.040222406387329, 0, 0, 0, 0, -0.0011826878180727363, 0, 0, 0, 0, 0, 0, -0.4388015866279602, 0, 0, 0, 0, 0, 2.7433321475982666, -0.5402259230613708, 0, 0, -2.3719146251678467, 0, -1.5772511959075928, -2.3719146251678467, -2.3719146251678467, 0, -0.9282531142234802, 0, -0.5681599378585815, -0.7180578112602234, -1.40459406375885, 3.1551015377044678, -0.684945821762085, -5.0214385986328125, -0.041377391666173935, 0, -0.5790233016014099, -0.5005801916122437, 0, 0, -0.021866243332624435, 0, -0.5005801916122437, -0.012498320080339909, -0.012498320080339909, -0.9583289623260498, -1.9684401750564575, -0.021866243332624435, 0, -7.0164055824279785, -2.1590864658355713, 5.193119049072266, 0.6848592758178711, -0.8103779554367065, 2.573368549346924, 3.896730899810791, 3.896730899810791, -0.2532079815864563, 1.5485568046569824, -4.852644443511963, -5.013269901275635, 0.16636477410793304, 2.827939510345459, -0.00019315173267386854, 0, -2.3719146251678467, -2.3719146251678467, 0, 0, -0.07603735476732254, -0.07603735476732254, -3.366123914718628, -3.244893789291382, -2.5147147178649902, -0.046083759516477585, -0.046083759516477585, 0, 9.850391387939453, 7.584823131561279, 0.5489126443862915, -1.207241415977478, -1.5674493312835693, -1.5674493312835693, -6.217570781707764, -0.8235598802566528, -4.127623558044434, -0.22487030923366547, -3.4819133281707764, -3.4819133281707764, -0.2731207013130188, 0, -0.05721382424235344, -3.482177972793579, -3.482177972793579, -0.05721382424235344, -0.0011826878180727363, 0, 0, 0, 0, 0, 0, 0, 0, -1.6106719970703125, -0.004084337968379259, -0.004084337968379259, -0.004084337968379259, -0.04013250768184662, -0.04013250768184662, -0.06050543859601021, -0.06050543859601021, -0.06050543859601021, -0.007155412342399359, -0.08259169012308121, -0.08312001824378967, -0.08259169012308121, -0.016086788848042488, -0.03447313606739044, -0.00019315173267386854, -0.00019315173267386854, -0.00019315173267386854, -0.01034295093268156, -1.6106719970703125, -2.4494247436523438, -0.07211693376302719, 0, -0.9282531142234802, -0.9282531142234802, 0, 0, 0, -0.07211693376302719, 0, 0, 0, 0, -0.07211693376302719, 0, 0, 0, 0, 0, 0, 0, -0.0011826878180727363, 0, 0, -0.0011826878180727363, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.1503511447949455], [0, 0, -1.4399656057357788, 0, 0, -1.4326673746109009, 0, -0.0006743143894709647, -1.3943233489990234, 0, 0, 0, 0, 0, -0.22265996038913727, 0, 0, 0, 0, 0, 0, -0.14006251096725464, 0, 0, 0, 0, 0, -0.7594014406204224, 0, -0.05024879053235054, -0.05024879053235054, 1.4677088260650635, 0, -0.04519722983241081, 0.919623613357544, 1.5207918882369995, -0.6734616160392761, 0, 0, -3.6671903133392334, 0, 0, -3.1237101554870605, 0, 1.1850711107254028, -0.0006743143894709647, 0, -0.03336254134774208, 0, 0, -1.0968276262283325, -1.1939479112625122, 0, 0, -1.0968276262283325, 0, -0.8813648223876953, -1.6469016075134277, -0.13021141290664673, 0, -1.6203423738479614, -1.9005047082901, -1.348556399345398, -4.021496295928955, -1.26847243309021, -3.8111581802368164, -3.6261777877807617, -3.6261777877807617, 0, -2.702420234680176, 8.270593643188477, 2.3308603763580322, -2.914830446243286, 5.589139938354492, -0.0009187255636788905, 0, 2.2837865352630615, 1.1428884267807007, 0, 0, -0.7294782996177673, -0.7294782996177673, -2.509596347808838, -0.04627915471792221, -2.763197422027588, 0, -0.6734616160392761, 0, 5.192557334899902, 11.322322845458984, 0, 0.9919248223304749, -0.25367382168769836, -0.25367382168769836, -0.5579798221588135, -1.1216341257095337, -1.1608768701553345, -0.10236846655607224, -0.16083656251430511, -0.16083656251430511, -0.008774503134191036, -0.09746043384075165, -0.5565861463546753, -0.4735325276851654, -0.4735325276851654, -0.40701544284820557, -0.5781652927398682, -0.5509355068206787, -0.06118152663111687, -0.025710774585604668, -0.5611470937728882, -0.5611470937728882, -0.5611470937728882, -0.8308253884315491, -0.8308253884315491, -2.1855735778808594, -0.11876297742128372, -0.11876297742128372, -0.09853506088256836, -0.021938612684607506, -0.021938612684607506, -0.03336254134774208, -0.03336254134774208, -0.03336254134774208, -0.5059611797332764, 0, -0.0009187255636788905, 0, -0.018341200426220894, -0.24304725229740143, -0.0009187255636788905, -0.0009187255636788905, -0.008774503134191036, -0.21095013618469238, -0.9809674620628357, 0, -0.02261536382138729, -0.0006743143894709647, -6.885588663863018e-05, 0, -0.43887779116630554, 0, 0, -0.06623827666044235, 0, 0, 0, 0, 0, -6.885588663863018e-05, 0, 0, 0, 0, 0, -0.0392458438873291, -0.0392458438873291, -0.4925048053264618, 0, 0, -0.22265996038913727, -0.22265996038913727, -0.22265996038913727, -0.007396414410322905, 0, 0, 0, 0, 0, 0, 0, 0, 0, -6.885588663863018e-05, 0, 0, 0, -6.885588663863018e-05, 0, 0, 0, 0, 0, 0, -0.007396414410322905, -0.01479568425565958, 0, 0, 0, 0, 0, 0, 0, -3.8245817191387066], [-0.16567018628120422, -0.5175153017044067, -0.3732078969478607, -0.5168123245239258, -0.5155949592590332, -1.7023924589157104, 0, 0, -2.245868682861328, -0.4504788815975189, -0.5135281682014465, -0.2629076838493347, -0.2629076838493347, 0, -0.5802192091941833, -0.26270443201065063, -0.26270443201065063, -0.5113857388496399, -0.5097657442092896, -3.2190372943878174, -3.2190372943878174, 6.016255855560303, -0.25540006160736084, -0.25540006160736084, -0.25532352924346924, -0.25532352924346924, -0.49460944533348083, -1.7936397790908813, -0.13474109768867493, -0.013996867462992668, -0.013996867462992668, -0.10959155112504959, 0, -0.4591878652572632, -0.21451054513454437, -0.07839088141918182, -0.4029470980167389, -1.749589204788208, 0, 0.17757003009319305, -0.1666240096092224, -0.18420720100402832, 0.07369233667850494, -0.012560117989778519, -0.3465014398097992, -0.01737523451447487, 0, -0.31455346941947937, -0.07263410836458206, -0.20775863528251648, -0.008649690076708794, -0.44848790764808655, 0, -0.07263410836458206, -0.7853028178215027, -0.7680372595787048, -2.0703139305114746, 1.5563862323760986, -1.0248745679855347, 0, -2.7835276126861572, -3.0898773670196533, -1.1355971097946167, 0.5686129927635193, 2.0190680027008057, -0.3518427908420563, -0.1470891535282135, -0.1470891535282135, -0.648620069026947, -0.8837968707084656, -1.9310916662216187, 0, -1.1002330780029297, 1.668035626411438, -0.4961688220500946, 0, -0.07839088141918182, -0.19168145954608917, 0, 0, -0.04936092719435692, -0.04936092719435692, -1.176008939743042, -0.6015902161598206, -1.4868032932281494, 0, -0.4029470980167389, 0, -2.414266347885132, -7.926529884338379, -1.0051698684692383, -0.3524651527404785, 7.32917594909668, 6.190587997436523, 4.7694878578186035, 3.2175416946411133, 1.734645128250122, 13.622880935668945, -4.063429832458496, -4.063429832458496, -1.0984498262405396, -2.67374849319458, -2.741665840148926, -0.8477502465248108, -0.8477502465248108, -0.03395841643214226, -0.39455315470695496, -0.8093279600143433, -0.6025065183639526, -0.4226973056793213, -0.06265286356210709, -0.06265286356210709, -0.06265286356210709, -0.03821690380573273, -0.03821690380573273, -0.2531993091106415, -0.2125740796327591, -0.2125740796327591, -0.2125740796327591, -0.13892094790935516, -1.0249290466308594, -0.20501437783241272, -0.20501437783241272, -0.20501437783241272, -0.3524651527404785, -0.16929061710834503, -0.712932288646698, -0.16929061710834503, -0.08078353852033615, -0.25803419947624207, -0.4961688220500946, -0.4961688220500946, -0.4961688220500946, -0.13291926681995392, -0.22126197814941406, -0.27748581767082214, -1.174092173576355, 0, -0.12933123111724854, -0.12933123111724854, 0, 0, 0, -1.0386487245559692, -0.5780268311500549, 0, 0, 0, -1.0336183309555054, 0, 0, -0.5780268311500549, 0, -0.0033331571612507105, 0, 0, 0, 0, 0, 0, -0.5735226273536682, -0.5735226273536682, -0.5735226273536682, 0, 0, -0.0033331571612507105, 0, 0, 0, -0.5780268311500549, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.5780268311500549, -0.5780268311500549, -1.1388427019119263, -0.8471541404724121, 0, -0.07879199087619781, -0.21132710576057434, -0.21132710576057434, -0.21132710576057434, -0.21132710576057434, 0, 0, 0, 0.5167317717424064], [0, 0, -0.11902274191379547, 0, 0, -2.4895894527435303, 0, -0.07744522392749786, -2.1390433311462402, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.281802773475647, -0.281802773475647, 2.1651861667633057, 0, 0, 0, 0, 0, -0.6426544189453125, 0, -1.854900598526001, 0, 0, 0, -0.5051620006561279, -2.1046807765960693, 0, -0.33003711700439453, -1.5063786506652832, 0, -2.351839303970337, -0.0014112970093265176, -0.0014112970093265176, 2.3558619022369385, 0, -0.5161470174789429, -0.31432023644447327, 0, -0.097023606300354, 0, -0.3343188762664795, -0.04743694141507149, -0.12747251987457275, -0.048625875264406204, 0, -0.04743694141507149, 0, -0.2494998425245285, -0.5362832546234131, -0.03259865194559097, -0.048625875264406204, 0.09755221009254456, -0.10973413288593292, -0.3411736488342285, -2.003706455230713, 0.08203539252281189, -0.39420604705810547, 1.263403296470642, 1.263403296470642, 3.352135419845581, -0.22796937823295593, -1.1112648248672485, -0.36588719487190247, -0.5868193507194519, -0.9045543074607849, -0.13918370008468628, 0, 0, -3.0218617916107178, 0, 0, 0, 0, -0.8977647423744202, -0.2789630889892578, 0.9095597267150879, 0, -0.33003711700439453, 0, -0.04474828392267227, -2.0176889896392822, -0.3341432809829712, -0.09714750945568085, -0.7784096002578735, -0.7784096002578735, 4.5467023849487305, -1.0783861875534058, 1.8787580728530884, 0, 7.054728984832764, 7.054728984832764, 3.5230445861816406, 4.4769415855407715, 5.5047125816345215, -0.9877743124961853, -0.9877743124961853, -0.8246482014656067, -0.09784629195928574, -0.14545795321464539, -1.9551037549972534, -0.2488091140985489, -0.11692232638597488, -0.11692232638597488, -0.11692232638597488, -0.09470510482788086, -0.09470510482788086, -2.956467390060425, -0.10122393071651459, -0.10122393071651459, -0.10122393071651459, -0.08750203251838684, -0.08750203251838684, -0.097023606300354, -0.097023606300354, -0.097023606300354, -0.09714750945568085, -0.11798594146966934, -0.3079531490802765, -0.11798594146966934, -0.04142531752586365, -4.001764297485352, -0.13918370008468628, -0.13918370008468628, -0.13918370008468628, -0.04307815805077553, -0.04474828392267227, -0.5635775327682495, -0.0184071883559227, -0.07744522392749786, -0.01987798884510994, -0.01987798884510994, 0, -0.07981088012456894, -0.07981088012456894, -0.23363807797431946, -1.4666224718093872, -1.4666224718093872, -1.4666224718093872, -1.4666224718093872, -0.0184071883559227, -1.854900598526001, -1.854900598526001, -1.854900598526001, -2.77209734916687, -2.77209734916687, -3.8327572345733643, -3.8327572345733643, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -2.4292504606692487], [0, 0, -0.1417900025844574, 0, 0, -2.9567577838897705, -2.3445260524749756, -0.009471939876675606, -1.8771830797195435, -0.1819235384464264, 0, -0.08097276836633682, 0, 0, -0.06335993111133575, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.4036599099636078, 0, 0, 0, 0, 0, 0.21630513668060303, -0.2244187891483307, 0, -0.16203095018863678, -0.25530558824539185, 0, 0.6747213006019592, -0.1819235384464264, -0.1819235384464264, 1.1715633869171143, 0, -0.7855943441390991, -0.15027867257595062, 0, -0.09169580787420273, 0, 0, -0.037508394569158554, -0.1384911835193634, 0, 0, -0.037508394569158554, 0, -0.15136031806468964, -0.24981485307216644, -0.01531205978244543, 0, -1.0980550050735474, -0.999129056930542, 0, -0.6068300008773804, -0.47976920008659363, 0.8985050916671753, 1.8468934297561646, 1.8468934297561646, 0, -0.19691918790340424, -0.9549363851547241, -0.35844406485557556, 1.0019499063491821, 0.8843674063682556, -0.030598966404795647, 0, 0, -3.8664348125457764, 0, 0, -0.0911882072687149, -0.0911882072687149, -1.470470666885376, -0.6238093376159668, -1.0047519207000732, -0.007787150330841541, -0.06623110920190811, -0.34876301884651184, -1.6486401557922363, -1.9382152557373047, 2.8386263847351074, -0.09211855381727219, -0.25624650716781616, -0.25624650716781616, -0.05828740447759628, -0.22917650640010834, -0.14670778810977936, 0, -0.9790470004081726, -0.9790470004081726, -1.4955068826675415, 0, 1.2250195741653442, 5.952133655548096, 6.335219383239746, 1.6081089973449707, -0.10401179641485214, -0.05511539801955223, -0.40568381547927856, -0.06864465773105621, -0.03583521023392677, -0.03583521023392677, -0.03583521023392677, -2.0146241188049316, -0.026593292132019997, -1.4291132688522339, -0.09265471994876862, -0.09265471994876862, -0.09265471994876862, -0.1417900025844574, -0.1417900025844574, -0.09169580787420273, -0.09169580787420273, -0.09169580787420273, -0.09211855381727219, -0.13167539238929749, -2.2141849994659424, -0.13167539238929749, -0.04184839501976967, -0.3602369427680969, -2.0226356983184814, -0.030598966404795647, -1.4861767292022705, -0.10878484696149826, -0.05071423202753067, -0.09304894506931305, -0.2244187891483307, -0.009471939876675606, -1.3757832050323486, -0.0405656099319458, -0.10693851113319397, 0, 0, 0, -0.013687873259186745, -0.013687873259186745, -0.013687873259186745, -0.013687873259186745, 0, 0, -0.007787150330841541, -0.007787150330841541, -0.37735965847969055, -1.2968600988388062, -0.0037417521234601736, -0.0037417521234601736, -0.004665133077651262, 0, -0.007787150330841541, -0.004665133077651262, -0.0013784242328256369, -0.0013784242328256369, -0.0013784242328256369, -0.08097276836633682, -0.08097276836633682, -0.08097276836633682, -2.318352699279785, -2.318352699279785, -1.424978494644165, -1.424978494644165, -1.424978494644165, -1.424978494644165, -1.9614379405975342, -1.294651985168457, -1.294651985168457, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.7289718346098373], [0, 0, -0.1240120381116867, 0, 0, -1.2685763835906982, -1.673594355583191, 0, -1.132789969444275, -0.0056479633785784245, 0, 0, 0, 0, -0.022335469722747803, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.030100204050540924, 0, -0.03138947859406471, 0, 0, 0, -0.20206813514232635, -0.05102865770459175, 0, 1.0716474056243896, -1.5230975151062012, 0, -2.5017757415771484, -0.0056479633785784245, -0.0056479633785784245, -0.5831568241119385, 0, -4.040413856506348, -0.03141966834664345, 0, -0.15324431657791138, 0, 0, -0.7832635045051575, -1.0500385761260986, 0, 0, -0.7832635045051575, 0, -0.01630149409174919, -0.5995748043060303, -0.1463068574666977, 0, -1.7810434103012085, -2.0308995246887207, 0, -2.345414638519287, 2.9163081645965576, -0.8606514930725098, 0, 0, 0, -0.05437992513179779, 4.872375965118408, -0.009664017707109451, -0.009664017707109451, -1.5296404361724854, -0.37681838870048523, 0, 0, -1.9194635152816772, 0, 0, 0, 0, -0.8696383237838745, -0.7040199637413025, 4.149637699127197, 0, 1.081911563873291, 0, -0.8699662089347839, -0.9098776578903198, -0.15167507529258728, -0.3369949758052826, -0.8527504801750183, -0.8527504801750183, -0.6539708375930786, 2.7221169471740723, -1.7784072160720825, 0, -0.2770819067955017, -0.2770819067955017, -0.4011329412460327, -0.03138947859406471, -1.6680701971054077, -1.6898564100265503, -0.05171328783035278, -0.03411625698208809, 1.076988697052002, 3.2420003414154053, 0.3628614842891693, 5.181088447570801, 1.986188292503357, 1.986188292503357, 1.986188292503357, 2.952040672302246, 3.0057311058044434, -0.8502824902534485, -0.11676369607448578, -0.11676369607448578, -0.11676369607448578, -0.1240120381116867, -0.1240120381116867, -0.15324431657791138, -0.15324431657791138, -0.15324431657791138, -0.16065248847007751, -0.08209826797246933, -0.600417971611023, -0.08209826797246933, -0.09121913462877274, -0.2834266424179077, -0.43686285614967346, -0.37681838870048523, -0.3950589597225189, -0.07560854405164719, -0.02875807322561741, -0.5511472821235657, -0.07818569242954254, 0, -1.0368961095809937, -0.007250423543155193, -3.757164239883423, -0.04877564683556557, -0.04877564683556557, -0.0016427087830379605, -0.05041000247001648, -0.05041000247001648, -0.05041000247001648, -0.05041000247001648, -0.0016427087830379605, -1.022423505783081, 0, 0, -1.6164175271987915, 0, -0.020081521943211555, -0.020081521943211555, -0.0024614809080958366, -3.757164239883423, 0, -0.0024614809080958366, -0.00859712716192007, -0.00859712716192007, -0.00859712716192007, 0, 0, 0, 0, 0, -0.008146926760673523, -0.008146926760673523, -0.008146926760673523, -0.008146926760673523, -0.03745616599917412, -1.022423505783081, 0, -4.455059051513672, -4.455059051513672, -1.022423505783081, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.6954831290565132], [-0.21843843162059784, -1.0082058906555176, -1.0853139162063599, -0.7233392000198364, -0.7106896042823792, -3.9106314182281494, -0.1274140179157257, -0.028133537620306015, -3.7521164417266846, -0.4382345676422119, -0.6950822472572327, -0.42091917991638184, -0.42091917991638184, -0.1983751356601715, -0.8710982203483582, -0.36495497822761536, -0.5154851675033569, -0.6684103608131409, -0.6558291912078857, -0.2909497320652008, -0.2909497320652008, -0.2909497320652008, -0.39571335911750793, -0.39571335911750793, -0.39104095101356506, -0.39104095101356506, -0.63679438829422, -1.8539859056472778, -0.12549424171447754, -0.27608194947242737, -0.26295721530914307, -0.3445270359516144, -1.6868202686309814, 2.0660653114318848, -0.3845518231391907, -0.055983517318964005, -1.3221396207809448, -1.0981484651565552, -0.2378728836774826, -1.7131373882293701, -0.35454872250556946, -0.37794485688209534, -0.516991376876831, -0.008267959579825401, 1.7428032159805298, -0.26568859815597534, -0.14244359731674194, 0.032878726720809937, -1.188482403755188, -1.188283085823059, 2.231092691421509, 0.7539051175117493, -3.390364170074463, -0.22358104586601257, 4.275034427642822, -1.346961498260498, -1.5175286531448364, -1.7888875007629395, -1.0351226329803467, 0, -3.021566867828369, -0.49198222160339355, 0, -0.5842194557189941, -0.16011154651641846, -0.346122682094574, -0.09506966173648834, -0.09506966173648834, -0.04303474724292755, -0.10472916066646576, 1.9635764360427856, -0.35916614532470703, -0.35916614532470703, -2.6989903450012207, -0.27884793281555176, -0.04303474724292755, -0.003886861028149724, 0.7213437557220459, 0, 0, 0, 0, -0.7758949398994446, -0.3223777711391449, -1.4734089374542236, 0, -0.43440914154052734, 0, -2.424415349960327, -0.4956190884113312, -0.19893702864646912, 5.805569171905518, -0.002745848149061203, -0.028855551034212112, -0.05154965817928314, -0.5539692044258118, -0.22825944423675537, -0.4525396525859833, -1.1416280269622803, -1.1416280269622803, -0.28885310888290405, -0.00666653411462903, -0.4609096348285675, -0.8383323550224304, -0.8035173416137695, -0.40387415885925293, -0.347875714302063, -0.3181188404560089, -0.22522084414958954, 0, -0.29073813557624817, -0.29073813557624817, -0.29073813557624817, -0.4832461476325989, -0.38706281781196594, 3.948429584503174, 2.9513895511627197, 2.9513895511627197, 6.252529144287109, 6.4740190505981445, 6.446710109710693, 4.622222900390625, 4.622222900390625, 4.622222900390625, 8.081687927246094, -0.16165994107723236, -0.5409680604934692, -0.16165994107723236, -0.2379743754863739, -0.5917181968688965, -0.313330739736557, -0.19782665371894836, -0.2637292742729187, -0.07278270274400711, -0.07408183068037033, -0.04372269660234451, -0.34606921672821045, -0.014906293712556362, -0.6865744590759277, -0.0436970517039299, -0.3955032527446747, -0.002743388991802931, -0.002743388991802931, -0.1531442552804947, -0.06751703470945358, -0.014389670453965664, -0.014389670453965664, -0.014389670453965664, -0.11652694642543793, -0.00666653411462903, -0.00666653411462903, -0.00666653411462903, -0.9618304371833801, -1.5263859033584595, -0.15108875930309296, -0.23067165911197662, -0.07065438479185104, -0.40386655926704407, 0, -0.011611973866820335, -0.04436567425727844, -0.04436567425727844, -0.04436567425727844, 0, 0, 0, -0.01861877180635929, -0.01861877180635929, -0.032507460564374924, -0.032507460564374924, -0.032507460564374924, -0.032507460564374924, -0.07545481622219086, -0.599180281162262, -0.599180281162262, 0, 0, 0, -0.042335111647844315, -0.042335111647844315, 0, 0, -0.01771187037229538, -0.01771187037229538, 0, 0, 0, 0, 0, 0, 0, -0.22617097198963165, -0.22617097198963165, 0.3836706383105466], [-0.039002303034067154, -0.12515389919281006, -0.11900335550308228, -0.07676750421524048, -0.07442501187324524, 1.0748136043548584, -0.9351599216461182, -0.14167346060276031, -2.636911392211914, -0.1279682219028473, -0.07095880806446075, -0.05916493758559227, -0.0488428957760334, 0, -0.3415835499763489, -0.0533810630440712, -0.0533810630440712, -0.06752440333366394, -0.06557770073413849, -0.03764896094799042, -0.03764896094799042, -0.03764896094799042, -0.044933021068573, -0.044933021068573, -0.04555998742580414, -0.04555998742580414, -0.059764087200164795, -0.25778910517692566, 0, -0.011348768137395382, -0.011348768137395382, -0.03932470455765724, 0, -0.8926600217819214, -0.5761132836341858, -0.43128305673599243, -0.06653325259685516, -0.15570895373821259, -0.12662054598331451, 0.4221881330013275, -0.026542751118540764, -0.026542751118540764, -2.221304416656494, 0, -0.9353317618370056, -0.4275389015674591, 0, -0.02285628765821457, -0.02285628765821457, -0.0829671248793602, 0, -0.2783525884151459, 0, 0, 0, 0, -0.05402152240276337, -0.5352594256401062, -2.449871063232422, 0, -0.7263434529304504, -0.8366802334785461, -1.0281100273132324, -1.3238595724105835, -0.8197617530822754, -0.8356072306632996, -0.8995640277862549, -0.8995640277862549, -0.5769040584564209, -0.47519612312316895, -0.5979645252227783, -0.7899514436721802, -1.6464158296585083, 1.3937222957611084, 1.8882278203964233, -0.5769040584564209, -0.2627626359462738, -1.5360946655273438, 0, 0, -0.8173883557319641, -0.8173883557319641, 1.8806675672531128, -0.07835791260004044, -0.7499911785125732, -0.3792717158794403, -0.3917272984981537, -0.019949525594711304, -0.48050379753112793, -0.3645961284637451, -0.3965822458267212, 0, 0, -0.07252831012010574, -0.46207675337791443, 1.605028748512268, -0.3746480643749237, 0, -1.4858851432800293, -1.4858851432800293, 1.5198085308074951, 0, -1.1701823472976685, -1.142119288444519, -1.142119288444519, -1.1701823472976685, -0.24948309361934662, -0.2552015483379364, -2.638004779815674, 0, 0, 0, 0, -0.9351599216461182, 0, -0.28847822546958923, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.107605457305908, 5.651349067687988, 4.107605457305908, 6.645047664642334, 5.8407368659973145, -0.6201121807098389, 2.5133321285247803, 1.7311601638793945, 3.6767728328704834, -0.2901824712753296, -0.21505321562290192, -0.17992395162582397, -0.14167346060276031, -0.0054501378908753395, -0.0054501378908753395, -0.8773755431175232, -0.0018211876740679145, -0.0018211876740679145, -0.013632315210998058, -2.3540725708007812, -0.025154385715723038, -0.025154385715723038, -0.025154385715723038, -0.013632315210998058, 0, -0.027747217565774918, -0.027747217565774918, -0.0013412917032837868, -0.02430601604282856, -1.4328936338424683, -1.4328936338424683, -0.1369001865386963, 0, -0.027747217565774918, -0.1369001865386963, -0.02590247057378292, -0.02590247057378292, -0.02590247057378292, -0.4416050612926483, -0.005181259009987116, -0.031025957316160202, -0.008081410080194473, -0.008081410080194473, -0.3089391589164734, -0.3089391589164734, -0.15365725755691528, -0.15365725755691528, -0.9351599216461182, 0, 0, -0.21542014181613922, -0.21542014181613922, 0, -0.11136923730373383, -0.11136923730373383, 0, 0, -0.07252831012010574, 0, -0.43128305673599243, 0, -0.07252831012010574, -0.07252831012010574, -0.07252831012010574, -0.07252831012010574, 0, -0.04811234399676323, -0.04811234399676323, -0.0031053947242295], [-0.016467852517962456, -0.06696243584156036, -0.5949281454086304, -0.017520422115921974, -0.016800783574581146, 1.1540087461471558, -0.03580917790532112, -0.16316314041614532, 1.0130246877670288, -0.12600190937519073, -0.0153597891330719, -0.12973202764987946, -0.009199707768857479, -0.0007174007478170097, -0.5312320590019226, -0.020203009247779846, -0.020203009247779846, -0.013756556436419487, -0.013191179372370243, -0.011624275706708431, -0.011624275706708431, -4.466551780700684, -0.011738238856196404, -0.011738238856196404, -0.011265354230999947, -0.011265354230999947, -0.011019366793334484, -3.6069347858428955, 0, -0.1744907647371292, 0, -0.1572495847940445, 0, -0.2883184254169464, -0.43784821033477783, 0, -0.05153574422001839, -1.6937655210494995, -0.017411110922694206, 1.7648240327835083, -0.15855474770069122, -0.19335463643074036, -1.274452805519104, -0.01638801209628582, -1.6173443794250488, -0.5977644324302673, 0, -0.006143744569271803, -0.006143744569271803, -0.10058705508708954, 0, -0.6377168893814087, 0, 0, 0, 0, -0.10726559162139893, -1.7085031270980835, -0.658341646194458, 0, -1.1640757322311401, -1.7439336776733398, -3.416677951812744, 0.08748458325862885, 1.2331829071044922, -0.904268205165863, -2.8553690910339355, -2.8553690910339355, -0.5706892013549805, -0.7200924158096313, -0.2610418200492859, -1.3097288608551025, -1.5902166366577148, -0.557436466217041, 0, 0, 0, -1.1627799272537231, 0, 0, -0.03251580148935318, -0.03251580148935318, -0.5243309140205383, -1.2935452461242676, -0.2767298221588135, -0.9422242045402527, -0.05153574422001839, -0.005652250722050667, 1.8459235429763794, 8.591023445129395, -0.42287296056747437, 0, -1.3239569664001465, -1.6989763975143433, 5.840853691101074, -1.2397302389144897, 5.787976264953613, 0, -1.5190801620483398, -1.5190801620483398, -0.08521756529808044, -0.1744907647371292, -0.4418351948261261, -0.1858932375907898, -0.17459629476070404, -0.3880380690097809, -0.11465980857610703, 0, -0.7451347708702087, 0, 0, 0, 0, -0.02428424544632435, 0, 4.28867244720459, 0, 0, 0, 0, -0.2919504940509796, 0, 0, 0, 0, 0, -0.02428424544632435, 0, 0, -0.2909858822822571, -0.02428424544632435, 0, -0.007500232197344303, 0, 3.611440420150757, 2.7166669368743896, -0.6087260842323303, -0.16316314041614532, -0.27731087803840637, -0.1513049453496933, -0.3705160319805145, -0.09020809829235077, -0.09020809829235077, -0.1560528725385666, -0.17358990013599396, -0.24724258482456207, -0.12600386142730713, -0.12600386142730713, -0.12757761776447296, -0.1744907647371292, -1.1991041898727417, -1.216913104057312, -0.014423037879168987, -0.27115726470947266, 0, -0.0014088599709793925, -0.07608050107955933, -0.04210145026445389, -0.9422242045402527, -0.06609418243169785, -0.5671715140342712, -0.08051242679357529, -0.08051242679357529, -0.08495426177978516, -0.08495426177978516, -0.3035288453102112, -0.02816934883594513, -0.02816934883594513, -0.007500232197344303, -0.02221684344112873, -0.007500232197344303, -0.007500232197344303, -0.02428424544632435, -0.07402266561985016, -0.07402266561985016, -0.1818847507238388, -0.1818847507238388, 0, 0, 0, -0.009091436862945557, -0.009091436862945557, -0.36250540614128113, -0.2919504940509796, 0, -0.03430216386914253, -0.02464127726852894, -0.02464127726852894, -0.02464127726852894, -0.02464127726852894, -0.07603567838668823, -0.001165185240097344, -0.001165185240097344, -2.851318331736335], [0, 0, -1.2691009044647217, 0, 0, -1.188161849975586, -0.19692674279212952, 2.7054293155670166, 1.3565049171447754, -0.03013252280652523, 0, -0.1865471601486206, 0, 0, -1.4606152772903442, 0, -0.8462321758270264, 0, 0, 0, 0, -0.03255593776702881, 0, 0, 0, 0, 0, -0.19332924485206604, 0, -0.5745736360549927, 0, -0.27680662274360657, 0, -1.82952082157135, 2.188067674636841, -0.11638973653316498, -1.369476556777954, 0.6396822929382324, -0.6456029415130615, 2.8693771362304688, 0, 0, -0.18508128821849823, 0, 1.2661361694335938, 4.919086933135986, -0.8462321758270264, -0.038562119007110596, 0, -0.1787085086107254, -0.038562119007110596, 1.7681667804718018, -0.43783116340637207, 0, -0.0028643386904150248, -0.0028643386904150248, -0.1829908788204193, 1.0151132345199585, 1.106637954711914, -0.3757035434246063, 0.07841652631759644, -2.175565481185913, -1.547600269317627, 1.1344221830368042, -1.0170758962631226, 6.395088195800781, 0, 0, 0, 3.9125778675079346, -2.675347328186035, -0.2222229540348053, -2.450246572494507, -1.6370842456817627, 0, 0, 0, 0.22962579131126404, -2.0253419876098633, -2.0253419876098633, 0, 0, 0.3959251642227173, -2.813952922821045, -0.747685968875885, -0.17215435206890106, -0.2650884687900543, 0, -0.72903972864151, -0.027985166758298874, -0.5187413096427917, 0, 0, -0.20707085728645325, -0.060555022209882736, -0.1212528645992279, 0, 0, -0.9163655638694763, -0.9163655638694763, -0.2817014753818512, -0.5745736360549927, -1.3820282220840454, -0.36668217182159424, -0.36668217182159424, -1.3820282220840454, -0.04713813588023186, 0, 1.5215224027633667, 0, 0, 0, 0, 0, 0, -0.4703427255153656, -0.08922163397073746, -0.08922163397073746, 0, 0, -0.1571183204650879, 0, 0, 0, 0, 0, 0, 0, 0, -0.7154124975204468, -0.13504832983016968, 0, -0.1881617307662964, 0, -0.17215435206890106, -0.03013252280652523, 1.926271915435791, 2.9355411529541016, 1.7724337577819824, 1.9677183628082275, -0.41684627532958984, -0.3986196517944336, -0.3986196517944336, -1.3399158716201782, -0.6305804252624512, -0.3105822503566742, -0.3105822503566742, -0.3105822503566742, -0.914958655834198, -0.44807055592536926, -0.5394548773765564, -0.5947228074073792, -0.10591865330934525, -0.4703427255153656, 0, -0.45743241906166077, -0.5537963509559631, -0.45743241906166077, -0.17215435206890106, -0.04713813588023186, -0.2900134027004242, -0.17977184057235718, -0.17977184057235718, -0.38483479619026184, -0.1865471601486206, -0.53562331199646, 0, 0, -0.054784853011369705, -0.09381893277168274, -0.054784853011369705, -0.054784853011369705, 0, -0.13962985575199127, -0.023459071293473244, -0.08545538038015366, -0.08545538038015366, -0.09924615919589996, 0, 0, -0.02312595210969448, -0.02312595210969448, -0.20707085728645325, -0.1571183204650879, -0.10395193099975586, -0.603597104549408, -0.03013252280652523, -0.03013252280652523, -0.03013252280652523, -0.03013252280652523, 0, 0, 0, -2.88979795846277], [0, 0, -0.5684148669242859, 0, 0, 0.8307175040245056, -0.3446454107761383, -0.33301842212677, 0.31189483404159546, -0.34065642952919006, 0, -0.05548311024904251, 0, 0, -0.9244207739830017, 0, 0, 0, 0, 0, 0, -0.9806699752807617, 0, 0, 0, 0, 0, 0.978244960308075, -0.04146312177181244, -1.4982494115829468, 0, -0.14267006516456604, 0, 0.5611621141433716, -0.8469991087913513, -0.009484796784818172, -0.43684452772140503, -0.2688485383987427, -0.37714052200317383, 0.8381242156028748, -0.09186645597219467, -0.10730244219303131, -1.4219741821289062, -0.0031608818098902702, 0.17609845101833344, -0.8293108940124512, 0, -0.06143003702163696, -0.06143003702163696, 0, 0, -0.4375256896018982, 0, -0.06143003702163696, -0.005222184583544731, -0.005222184583544731, 1.698154330253601, -0.5204997062683105, -1.8073915243148804, 0, 2.015458106994629, 0.14256049692630768, -0.5468239188194275, -1.096683382987976, -0.3290865123271942, -2.455620765686035, -0.4184921383857727, -0.4184921383857727, -0.05289280414581299, -0.07494854927062988, -0.7805203795433044, 1.4237799644470215, 1.159477949142456, -2.8675642013549805, -0.05289280414581299, -0.05289280414581299, -0.5398710370063782, 0.11356412619352341, -0.0048050484620034695, -0.0048050484620034695, -0.11199092864990234, -0.11199092864990234, 1.4477275609970093, -1.276439905166626, -0.7568157911300659, -0.23254193365573883, -0.43684452772140503, -0.11863119900226593, 0, -0.021398207172751427, -0.495505154132843, 0, 0, -0.3103122115135193, -0.9806699752807617, -0.1569802165031433, 0, 0, -0.5787742137908936, -0.5787742137908936, -0.21405497193336487, -2.527315139770508, 4.2301554679870605, -3.039476156234741, -2.5640153884887695, 5.734010696411133, -0.12168470025062561, 0, 2.809823989868164, 0, 0, 0, 0, 0, 0, -0.2721671164035797, -0.20350885391235352, -0.20350885391235352, 0, 0, -0.10753031820058823, 0, 0, 0, 0, 0, 0, 0, -0.04230012744665146, 1.4970000982284546, -0.6277238130569458, 0, -0.08116689324378967, -0.3777078092098236, -0.23254193365573883, -0.15823476016521454, -0.2205052524805069, -0.33301842212677, -0.10731378942728043, 0, 2.634490728378296, 5.191481113433838, 5.191481113433838, -0.4037134647369385, -0.9771673083305359, -0.27235278487205505, -0.14224360883235931, -0.14224360883235931, -0.044477250427007675, -0.227957621216774, -0.3690814673900604, -0.3690814673900604, -0.4426688849925995, -0.2721671164035797, -0.06539638340473175, -0.7116039395332336, -0.7416020035743713, -0.6444780826568604, -0.23254193365573883, -0.07087364047765732, -0.23365165293216705, -0.04463162645697594, -0.04463162645697594, -0.09270945936441422, -0.05548311024904251, -0.2397654950618744, -0.2614540457725525, -0.2614540457725525, -0.053221844136714935, -0.053221844136714935, -0.016301332041621208, -0.016301332041621208, 0, -0.10731378942728043, -0.009083244018256664, -0.11331203579902649, -0.11331203579902649, -0.09141811728477478, -0.03484819456934929, -0.03484819456934929, 0, 0, -0.3103122115135193, -0.10753031820058823, -0.009484796784818172, -0.09663570672273636, -0.15823476016521454, -0.15823476016521454, -0.15823476016521454, -0.15823476016521454, -0.023426465690135956, 0, 0, -1.903725539484286], [-0.009842748753726482, -0.03854960575699806, -0.8593785166740417, -0.010478392243385315, -0.010048283264040947, 0.4966716170310974, -0.03423331677913666, -0.0037068540696054697, -0.037059932947158813, -0.05934245139360428, -0.009046392515301704, -0.11486964672803879, -0.0010701657738536596, 0, -0.6646279096603394, -0.012556739151477814, -0.012556739151477814, -0.008279063738882542, -0.007939202710986137, -0.007171437609940767, -0.007171437609940767, -0.007171437609940767, -0.007034399546682835, -0.007034399546682835, -0.006751737557351589, -0.006751737557351589, -0.006613070610910654, -0.5689369440078735, -0.0003593865840230137, 0.7279269695281982, -0.049655646085739136, -0.43722110986709595, 0, -0.8083940744400024, 2.7107160091400146, -0.4656526446342468, 1.318468451499939, 1.762818694114685, -0.12708885967731476, -1.805145025253296, -0.02566886879503727, -0.02566886879503727, -0.8622568845748901, 0, -1.771185278892517, -0.043373122811317444, 0, 0, 0, -0.08895424008369446, 0, -0.7728021144866943, 0, 0, -0.1128150150179863, -0.1128150150179863, -0.3720649480819702, 1.0667989253997803, -0.9464438557624817, 0, 0.17176780104637146, -0.08692308515310287, -0.27786439657211304, 3.4745917320251465, -2.316678047180176, -1.5671052932739258, -0.9077321290969849, -0.9077321290969849, -0.5282657742500305, -2.145763874053955, -0.32479211688041687, 0, -0.006640715058892965, -0.5982338190078735, 0, 0, 0, -0.7842434644699097, 0, 0, 0, 0, -0.2784130573272705, -0.8713337182998657, -0.7456631660461426, -0.2360185831785202, 1.893700122833252, 0, -0.23526541888713837, -0.06773340702056885, -0.5308971405029297, 0, -0.21033458411693573, -0.24448315799236298, -0.8130993843078613, -0.47181299328804016, -0.07909621298313141, 0, 1.5960336923599243, 1.5960336923599243, -0.9165741205215454, 0.8058977723121643, 0, -0.11423355340957642, -0.11423355340957642, 0, -0.25636184215545654, 0, 0.9641626477241516, 0, 0, 0, 0, -0.025371890515089035, 0, -0.48199352622032166, 0, 0, 0, 0, -0.009744183160364628, 0, 0, 0, 0, 0, -0.025371890515089035, 0, 0, -0.8632762432098389, -0.07748708128929138, 0, -0.3698877692222595, 0, -0.2360185831785202, -0.007000109180808067, 1.815291166305542, 0, -0.4322311282157898, -0.35136541724205017, -1.1484553813934326, 0, 0, 6.2167840003967285, 1.8133047819137573, 3.2573301792144775, 3.2573301792144775, 3.2573301792144775, 3.125748872756958, 1.6483709812164307, 1.413924217224121, 1.266110897064209, -0.3363332748413086, -0.48199352622032166, -0.794963538646698, -0.9091570973396301, -0.1537916511297226, -0.13518163561820984, -0.2360185831785202, -0.06992143392562866, -0.1364017277956009, -0.1364017277956009, -0.1364017277956009, -0.31108513474464417, -0.07586631923913956, -0.1800507754087448, -0.020073384046554565, -0.020073384046554565, -0.1508716493844986, -0.2983303666114807, -0.029270896688103676, -0.029270896688103676, -0.025371890515089035, -0.054172683507204056, 0, -0.09161017835140228, -0.09161017835140228, -0.054172683507204056, -1.2317461967468262, -1.2317461967468262, -0.09926758706569672, -0.09926758706569672, -0.02405242994427681, -0.009744183160364628, -0.1688358038663864, -0.004375954158604145, -0.007000109180808067, -0.007000109180808067, -0.007000109180808067, -0.007000109180808067, 0, -0.036282412707805634, -0.036282412707805634, -1.4134990075255778], [-0.038333192467689514, -0.17033740878105164, -1.0555452108383179, -0.04024381935596466, -0.0385872982442379, -2.221040725708008, -0.19423577189445496, 0, -1.8061903715133667, -0.12218370288610458, -0.03507619351148605, -0.1798083335161209, -0.022033289074897766, 0, -0.9692443013191223, -0.0435619056224823, -0.1119268462061882, -0.0314992219209671, -0.030202124267816544, -0.025603430345654488, -0.025603430345654488, -1.4595048427581787, -0.0270199216902256, -0.0270199216902256, -0.02592678740620613, -0.02592678740620613, -0.0253048874437809, -0.3703087270259857, -0.009679233655333519, 5.309338092803955, -0.8844778537750244, -1.2816917896270752, 0, -0.6111637353897095, -1.9308488368988037, 0, -0.3034820854663849, -1.256701946258545, -0.1605674773454666, 1.5948123931884766, -0.05178934708237648, -0.05178934708237648, -1.5453855991363525, 0, 0.8236873745918274, -0.22169837355613708, -0.053229741752147675, -0.053477365523576736, -0.016730638220906258, -0.2349637895822525, -0.38756680488586426, 0.5097491145133972, -0.5175594091415405, -0.0015831268392503262, -0.33404821157455444, -0.0004998339572921395, -0.0004998339572921395, -0.49156123399734497, 1.8334765434265137, -0.463234007358551, 1.119060754776001, 0.8732956647872925, -0.8403728604316711, -2.4488816261291504, -1.1480170488357544, -0.6206492185592651, -1.248488187789917, -1.248488187789917, -0.29222261905670166, -0.20571976900100708, -1.1252766847610474, -0.28401392698287964, -0.28401392698287964, -0.37051886320114136, -0.00040183059172704816, -0.00040183059172704816, -0.9793030023574829, 1.0604662895202637, 0, 0, 0, 0, -1.1788543462753296, -1.726616621017456, -2.703920841217041, -0.1259056180715561, -0.12267826497554779, 0, 0, 0, -0.4033091962337494, 0, 0, 0, -1.5875376462936401, -0.5430695414543152, -2.010803461074829, 0, 2.170584201812744, 2.170584201812744, -0.39069119095802307, 4.773914813995361, -1.631500482559204, -1.1032469272613525, -0.8997579216957092, -0.004059867933392525, -0.20589959621429443, 0, 2.6997179985046387, 0, 0, 0, 0, -0.03212794288992882, 0, 2.360294818878174, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.03212794288992882, 0, 0, 2.4793593883514404, -1.2349905967712402, 0, -0.012492869049310684, 0, -0.1259056180715561, 0, -0.39352405071258545, 0, -0.6831063628196716, 0, -0.12267826497554779, -2.0990421772003174, -2.0990421772003174, 0, -2.3798789978027344, -1.0150893926620483, -0.9483500123023987, -0.9483500123023987, 0, -1.0615708827972412, -1.3133821487426758, -1.5550942420959473, 3.5726561546325684, 2.694838047027588, 3.650057554244995, 5.901514530181885, 2.12766695022583, 2.0992953777313232, -0.1259056180715561, -0.06187814474105835, -0.04850633069872856, -0.04850633069872856, -0.04850633069872856, -0.0901571661233902, -0.0901571661233902, -0.5737943649291992, -0.0637066662311554, -0.0637066662311554, -0.012492869049310684, -0.17615628242492676, -0.012492869049310684, -0.012492869049310684, -0.03212794288992882, -0.6831063628196716, -0.6831063628196716, -0.2283741682767868, -0.2283741682767868, 0, -0.009527266956865788, -0.009527266956865788, -0.15429376065731049, -0.15429376065731049, 0, 0, 0, 0, 0, 0, 0, 0, -0.03336971625685692, -0.016165800392627716, -0.016165800392627716, -2.0868435585086873], [0, 0, -0.8471079468727112, 0, 0, -1.9660043716430664, -0.13429762423038483, -0.08091267198324203, 2.8192813396453857, -0.16068823635578156, 0, 1.4132031202316284, -0.1306995153427124, -0.5374696254730225, 1.883134126663208, 0, 0, 0, 0, 0, 0, 0, -0.3292959928512573, -0.3292959928512573, 0, 0, 0, -0.19448375701904297, 0, -0.1437990963459015, 0, -0.3963032364845276, 0, -1.0702166557312012, -0.6927311420440674, -0.4413388669490814, -0.5135771632194519, 0.9200667142868042, -0.25099995732307434, -0.8168635368347168, -0.054468534886837006, -0.054468534886837006, -0.5545381903648376, 0, -0.14947351813316345, -0.25099995732307434, 0, 0, 0, 0, 0, -0.4086986482143402, 0, 0, -0.05077133700251579, -0.05077133700251579, -0.1487898975610733, 1.5728123188018799, -0.2702655494213104, 0, -0.1277058720588684, -0.3154827654361725, -0.9034570455551147, -1.122278094291687, -1.0050976276397705, -0.4058995544910431, -0.2428576648235321, -0.2428576648235321, 0, -0.3106742203235626, -0.4910877048969269, -0.05073048919439316, -0.23481206595897675, 1.472893238067627, 0, 0, 0, -1.8220105171203613, 0, 0, -0.05911283567547798, -0.05911283567547798, -1.8777962923049927, 3.2590174674987793, -0.4665599465370178, 1.3948546648025513, -0.536839485168457, 0, -0.6610164046287537, -0.41837820410728455, 4.5301947593688965, 0, 0, -0.4706154763698578, -0.006973449606448412, -0.4045777916908264, -0.5395135879516602, 0, -0.1437990963459015, -0.1437990963459015, 0.2106320559978485, -0.1437990963459015, -0.013191594742238522, -0.6982311010360718, -0.6712278127670288, 0, 1.564588189125061, 0, -0.28628984093666077, 0, 0, 0, 0, -0.014787467196583748, 0, -1.120418667793274, 0, 0, 0, 0, -0.2926790118217468, 0, 0, 0, 0, 0, -0.014787467196583748, 0, -0.07544682919979095, -0.3983266055583954, -0.23614192008972168, 0, -1.3229740858078003, 0, 1.267998218536377, -0.06536829471588135, -0.3963032364845276, 0, -0.5067902207374573, -0.16529487073421478, -0.08616824448108673, 0, 0, 0, -0.6225161552429199, -0.2907485365867615, 0, 0, 0, -0.47525960206985474, 1.6526615619659424, 1.6444482803344727, -0.013191594742238522, -0.3963032364845276, 0, 0, 1.6652597188949585, -0.0718698650598526, 1.8457765579223633, 1.6652597188949585, 2.1363418102264404, 2.8734500408172607, 2.8734500408172607, 0.9869109988212585, 1.5512133836746216, 0.9683395624160767, -0.04569266736507416, -0.04569266736507416, -0.7305010557174683, -0.7481436729431152, -0.3141764998435974, -0.3141764998435974, -0.014787467196583748, -0.23345540463924408, 0, -0.04344342276453972, -0.04344342276453972, -0.23345540463924408, -0.28689104318618774, -0.28689104318618774, -0.004106608219444752, -0.004106608219444752, -0.4706154763698578, -0.2926790118217468, -0.4413388669490814, -0.4706573784351349, -0.06536829471588135, -0.06536829471588135, -0.06536829471588135, -0.06536829471588135, -0.2907485365867615, 0, 0, -1.3456498403101165], [-0.013264655135571957, -0.5733751654624939, -6.231495380401611, -0.01598813571035862, -0.01532992534339428, 1.327741026878357, -0.0290916059166193, -0.2285161018371582, -2.5111238956451416, 2.8244638442993164, -0.00925460271537304, -0.008703758008778095, -0.008703758008778095, -2.5914103984832764, 5.076562404632568, -0.0008264962816610932, -0.44367921352386475, -0.000791852711699903, -0.0007586612482555211, -0.0007268611225299537, -0.0007268611225299537, -0.0007268611225299537, -1.3688029050827026, -1.3688029050827026, 0, 0, 0, 0.7623559236526489, -1.1452972888946533, -0.004739147145301104, -0.004739147145301104, 2.008227586746216, -0.1729927361011505, 4.727871417999268, -0.23387779295444489, -0.20635724067687988, 2.370208978652954, 1.6216241121292114, 1.7765138149261475, -0.02392241731286049, 3.9461207389831543, 2.4537947177886963, 2.045961380004883, -1.49040687084198, -0.5360729098320007, 2.44686222076416, -0.4420294165611267, -0.01111891120672226, -0.01111891120672226, -0.5834210515022278, -0.6268916726112366, 2.953005790710449, 0, -0.01111891120672226, -0.6297569870948792, -0.0014326578238978982, -0.6768630146980286, 1.196799635887146, -1.2913647890090942, 0, -0.9362733364105225, -1.441001534461975, -0.02420656941831112, -0.3327403664588928, -0.4604343771934509, -0.00943795032799244, 0, 0, -0.27120378613471985, 0, -1.1681511402130127, 0, 0, -0.8286312818527222, -0.27120378613471985, -0.27120378613471985, -0.14523208141326904, 1.4292529821395874, 0, 0, 0, 0, -0.4606945812702179, -0.34733495116233826, -0.384752482175827, 0, -0.1151881292462349, 0, -0.1387367993593216, 0, -0.17417341470718384, 0, -0.012399242259562016, -0.39103174209594727, 0, -0.7225753664970398, 0, 0, -0.7566242218017578, -0.7566242218017578, -0.41393834352493286, 0, -0.6604108810424805, -1.473755121231079, -1.4625259637832642, -0.6491992473602295, -0.23989515006542206, 0, -0.36163219809532166, 0, 0, 0, 0, -0.0156572163105011, 0, 0.5198782682418823, -0.005328364670276642, -0.005328364670276642, 0, 0, -0.24338670074939728, 0, 0, 0, 0, 0, -0.0156572163105011, 0, 0, -0.08709830045700073, -0.11058412492275238, 0, -0.17199526727199554, 0, 0, -0.0384916216135025, 1.8948382139205933, -0.2285161018371582, -0.4035121500492096, 0, -0.1151881292462349, 0, 0, -0.2597201466560364, -0.4334343373775482, -0.38499370217323303, 0, 0, -0.2597201466560364, -0.12509644031524658, 0, -0.06597205996513367, -0.7678354978561401, 1.1468985080718994, 0, 0, -0.17417341470718384, -0.03338247165083885, 0, -0.17417341470718384, -0.22231876850128174, -0.17551173269748688, -0.17551173269748688, -0.03490087389945984, 0, 2.184044361114502, -1.0867054462432861, -1.0867054462432861, -0.10203948616981506, -0.208979994058609, -0.031929679214954376, -0.031929679214954376, -0.0156572163105011, -0.4035121500492096, -0.19066140055656433, -0.10654518753290176, -0.10654518753290176, -0.12509644031524658, -0.14548225700855255, -0.14548225700855255, -0.06597205996513367, -0.06597205996513367, -0.3586188554763794, -0.24338670074939728, -0.03490087389945984, -0.39108121395111084, -0.0384916216135025, -0.0384916216135025, -0.0384916216135025, -0.0384916216135025, -0.38499370217323303, -0.5834210515022278, -0.5834210515022278, -2.4748730215804726], [-0.026017358526587486, -0.15643413364887238, -0.6183673143386841, -0.0380677729845047, -0.036514364182949066, 0.5867924690246582, 1.8891068696975708, -0.060853153467178345, 0.04829616844654083, -0.04901425167918205, -0.03130712732672691, -0.02923387475311756, -0.02923387475311756, -0.01820937544107437, -0.6002894639968872, -0.034564949572086334, -0.034564949572086334, -0.026658527553081512, -0.02557050995528698, -0.02275824174284935, -0.02275824174284935, -0.02275824174284935, -0.03305167332291603, -0.03305167332291603, -0.020926261320710182, -0.020926261320710182, -0.020627783611416817, -0.08254389464855194, -0.019060293212532997, -0.005301611963659525, -0.005301611963659525, -0.44099241495132446, -0.014374952763319016, 0.6227185726165771, -0.3024503290653229, -0.3024503290653229, -0.12955616414546967, -0.042478423565626144, -0.1529127061367035, -1.7228361368179321, -0.0002865208953153342, -0.0030176755972206593, -1.3392353057861328, -0.0025878942105919123, -0.44182977080345154, -0.12438676506280899, 0, -0.04009322077035904, -0.022622331976890564, -0.030634086579084396, -0.07843738049268723, -0.5968784689903259, -0.016762016341090202, -0.0072777727618813515, -0.07484790682792664, -0.008027920499444008, -0.008027920499444008, -0.5405600070953369, -0.7440823316574097, -0.0018485536566004157, -0.17643961310386658, -0.7100946307182312, -0.008551240898668766, -0.0427643284201622, -0.03807811066508293, -1.3738255500793457, -2.1519064903259277, -2.1519064903259277, -0.0292036272585392, -0.0023156150709837675, -1.572625756263733, -0.00407763198018074, -1.255411982536316, -1.4468621015548706, -0.7971594929695129, 0, -0.45134347677230835, 3.107740879058838, 0, 0, -0.0038926261477172375, -0.0038926261477172375, -0.37052321434020996, -0.35631063580513, -1.4922829866409302, 0, -0.08927622437477112, 0, -0.037591252475976944, -0.01402275636792183, -2.9408137798309326, 0, 0, -0.07797080278396606, 0, -1.0426524877548218, 0, -0.005709840916097164, -0.5470624566078186, -0.5470624566078186, 0.6133215427398682, 0, -1.9279658794403076, 2.7107813358306885, 3.2757761478424072, -1.3639156818389893, -0.032576095312833786, 0, -0.17093750834465027, 0, 0, 0, 0, 2.5236310958862305, 0, 1.1252696514129639, -0.3685223460197449, -0.3685223460197449, 0, 0, -0.047314561903476715, 0, 0, 0, 0, 0, 1.7231365442276, 0, 0, -0.06895013898611069, 1.5852360725402832, -0.7971594929695129, 0.6575654149055481, 0, 0, -0.006998961325734854, -0.38757315278053284, 0, 1.3599869012832642, 0, -0.08927622437477112, 0, 0, 0, -1.3009394407272339, 0, 0, 0, 0, -0.7261531949043274, 0, -0.054765742272138596, -1.0579475164413452, 1.2056132555007935, 0, 0, 0, -0.032576095312833786, 0, 0, 0, 0, 0, -0.20678773522377014, 0, -0.38757315278053284, 3.825155735015869, 3.825155735015869, 1.7675549983978271, 1.6580232381820679, 2.749990463256836, 2.749990463256836, 2.5236310958862305, 1.3599869012832642, 2.0861399173736572, -0.06799158453941345, -0.06799158453941345, -0.7261531949043274, -1.0536088943481445, -1.0536088943481445, -0.054765742272138596, -0.054765742272138596, -0.07797080278396606, -0.047314561903476715, -0.20678773522377014, -0.08780626952648163, -0.006998961325734854, -0.006998961325734854, -0.006998961325734854, -0.006998961325734854, 0, -0.01360291987657547, -0.01360291987657547, -1.9455854378024318], [-0.03205789625644684, -0.18268518149852753, -1.028626799583435, -0.029757222160696983, -0.02850991301238537, 0.09423025697469711, 2.7644197940826416, -0.29964882135391235, 0.09423025697469711, -0.13954709470272064, -0.024316662922501564, -0.023297403007745743, -0.023297403007745743, -0.020264416933059692, -0.12998005747795105, -0.04293317347764969, -0.07810616493225098, -0.020571457222104073, -0.019709179177880287, -0.018433455377817154, -0.018433455377817154, -0.10990790277719498, -0.036524754017591476, -0.036524754017591476, -0.016417503356933594, -0.016417503356933594, -0.01572934351861477, -0.04493744671344757, -0.014755195006728172, -0.0022990345023572445, -0.0014264748897403479, -0.0014264748897403479, -0.0002003124973271042, -0.08056462556123734, -0.6329329609870911, -0.09017539769411087, -0.6778654456138611, -0.16076934337615967, -0.008890307508409023, -0.6112778782844543, 0, 0, -0.19761350750923157, 0, 3.5589826107025146, -0.13381460309028625, -0.02444213628768921, -0.046922340989112854, -0.02694248966872692, -0.06049559637904167, -0.10797560960054398, -0.46938246488571167, -0.020359359681606293, -0.009007262997329235, -0.10007013380527496, -0.00711084296926856, -0.00711084296926856, -0.47191324830055237, -1.492390751838684, -0.0033687129616737366, -2.4970576763153076, 1.0183603763580322, -0.09529244154691696, -0.26730668544769287, -2.9198811054229736, -1.2856074571609497, -0.09105634689331055, -0.09105634689331055, -0.007587237283587456, -0.030590277165174484, 3.075904369354248, -0.041954200714826584, -0.16787241399288177, -0.560835063457489, 0, 0, -0.031089577823877335, 0.1797131597995758, -0.09104085713624954, -0.09104085713624954, 0, 0, -0.3346361815929413, -0.2250107377767563, 3.321422815322876, -0.0059806182980537415, -0.5888054966926575, 0, -0.27592501044273376, -0.03437624126672745, 0, -0.09010060131549835, -0.35463184118270874, -0.657206118106842, -0.0741930827498436, -2.1616103649139404, -0.675548255443573, 0, -0.27383479475975037, -0.27383479475975037, -0.18680623173713684, -0.08450483530759811, 1.7291285991668701, 1.9120417833328247, -1.289817452430725, -1.3981174230575562, -0.18739229440689087, -1.7714253664016724, 2.4744601249694824, -2.5814833641052246, -1.2128856182098389, -1.2128856182098389, -1.2128856182098389, -1.8885140419006348, -1.7471368312835693, -0.9596208333969116, -0.4369407296180725, -0.4369407296180725, 0, 0, -0.0984402447938919, 0, 0, 0, 0, 0, -0.09994632005691528, 0, 0, -1.2755365371704102, -0.23086993396282196, 0, -0.18680623173713684, 0, 0, -0.05613936856389046, -0.2732710540294647, 0, 2.1388909816741943, 0, 3.946986198425293, 0, 0, 0, -0.6760578155517578, -0.04906098172068596, 0, 0, 0, 2.998013496398926, -0.00015932213864289224, -0.07063357532024384, 3.221961498260498, -0.8380633592605591, 0, -0.8958902955055237, -0.8958902955055237, 4.57630729675293, 0, 0, -0.038654226809740067, 0, 0, -0.09017539769411087, 0, 0, 0, 0, -0.06442055106163025, -0.13489480316638947, 0, 0, -0.09994632005691528, 2.1388909816741943, -0.8380633592605591, 5.468776226043701, 5.468776226043701, 2.9983327388763428, -0.4426455497741699, -0.4426455497741699, -0.04698283597826958, -0.04698283597826958, -0.20379973948001862, -0.0984402447938919, -0.09017539769411087, -0.09221024066209793, -0.05613936856389046, -0.05613936856389046, -0.05613936856389046, -0.05613936856389046, -0.04906098172068596, -0.008890307508409023, -0.008890307508409023, -2.754047745332354], [-0.0704311951994896, -0.2939799726009369, -0.6489894390106201, -0.07526175677776337, -0.07217051088809967, -0.3177129030227661, 0, -0.009438110515475273, -0.6341695785522461, -0.20113129913806915, -0.0653889998793602, -0.06147928535938263, -0.06147928535938263, -0.044076982885599136, -0.3116031587123871, -0.0802321508526802, -0.21390655636787415, -0.056210484355688095, -0.05390043184161186, -0.04890204221010208, -0.04890204221010208, -0.04890204221010208, -0.07723125070333481, -0.07723125070333481, -0.045262325555086136, -0.045262325555086136, -0.04426708072423935, -0.0875585749745369, -0.035452380776405334, -0.0050510140135884285, -0.0050510140135884285, -0.0050510140135884285, -0.0015448435442522168, -0.3812502920627594, -0.3204238712787628, -0.21669822931289673, 0.8200833201408386, -1.2934982776641846, -0.14275217056274414, 0.5701236724853516, 0, 0, -0.48020997643470764, 0, -0.5095218420028687, -0.19139020144939423, -0.107436902821064, -0.07656461000442505, -0.04931692034006119, -0.2360581010580063, -0.014105860143899918, -1.0113688707351685, -0.3627362549304962, -0.02144179306924343, -0.012791557237505913, -0.012791557237505913, -0.012791557237505913, -0.267917662858963, 2.808962106704712, -0.33474496006965637, -0.35989364981651306, -0.6773292422294617, -0.464775025844574, -0.688443124294281, -0.8005541563034058, -0.1402667760848999, -0.1186484545469284, -0.1186484545469284, -0.041335780173540115, -0.01417607069015503, -0.6122800707817078, -0.0012777174124494195, -0.048436399549245834, -0.44950076937675476, -0.5061967372894287, -0.006260309834033251, -0.0011920388787984848, -0.5402393341064453, -0.014361307956278324, -0.014361307956278324, -4.295530743547715e-05, -4.295530743547715e-05, 0.19624416530132294, 0.5634467005729675, -0.7028116583824158, -0.3051188588142395, 0.7254051566123962, -0.02067006565630436, 0, -0.004781682509928942, 0, 0, 0, -0.25120195746421814, -0.005626618396490812, 0.04968063533306122, -0.07858548313379288, -0.005427933298051357, -1.2024357318878174, -1.2024357318878174, 0.23241032660007477, 0, -0.26054200530052185, -0.49697035551071167, -0.49697035551071167, -0.26054200530052185, -0.5465043187141418, -0.017582301050424576, 1.6437911987304688, 0, 0, 0, 0, 0, 0, 0, -0.005426288116723299, -0.005426288116723299, 0, 0, -0.12693996727466583, 0, 0, 0, 0, -0.01734124682843685, -0.5274407267570496, -0.01734124682843685, -0.8988949060440063, 1.3659261465072632, 2.2234091758728027, -0.4936761260032654, 0.2485150545835495, -0.1833151876926422, 0, -0.060791999101638794, 0, -0.009438110515475273, 0, 0, 1.2856470346450806, 0, 0, -0.04620042443275452, 6.140499591827393, -1.3011088371276855, -1.0677685737609863, -1.0677685737609863, 0, 0, 0, 1.2263163328170776, 0, 0, 0, -0.1425008326768875, -0.1425008326768875, -0.3206368684768677, 0, 0, -0.1517796516418457, 0, 0, -0.21669822931289673, 0, 0, 0, 0, 1.0378880500793457, 2.26420259475708, -0.4898744523525238, -0.4898744523525238, 0, 0, 0, 0, 0, 0, 3.270390033721924, 3.270390033721924, 1.2263163328170776, 1.2263163328170776, -0.25120195746421814, -0.12693996727466583, -0.21669822931289673, -0.18656198680400848, -0.060791999101638794, -0.060791999101638794, -0.060791999101638794, -0.060791999101638794, -0.1598586142063141, -0.10253524780273438, -0.10253524780273438, -1.2266994599632268], [-0.03084574267268181, -0.21679459512233734, -2.6292102336883545, -0.02955280803143978, -0.02831406705081463, 2.5982985496520996, -0.4705128073692322, -1.0546983480453491, 2.203845500946045, 0.9056761860847473, -0.01851089484989643, -0.31416380405426025, -0.017734989523887634, -0.005121150054037571, -2.096341848373413, -0.03325650095939636, -0.03325650095939636, -0.016073722392320633, -0.015399972908198833, -0.014415318146348, -0.014415318146348, -0.014415318146348, -0.013811083510518074, -0.013811083510518074, -0.013232176192104816, -0.013232176192104816, -0.01267753355205059, -0.37949076294898987, -0.08117368817329407, -0.43041056394577026, 0, -1.0454121828079224, 0, -0.2981959879398346, 1.147666096687317, 2.1200876235961914, 0, -0.3848775327205658, -0.04169771075248718, 0.46990934014320374, 0, 0, 0.7115985155105591, 0, -1.3270926475524902, -0.4519699513912201, 0, -0.03471403196454048, -0.01875811070203781, -0.07075601816177368, -0.009269039146602154, -0.4897603690624237, -0.0207994282245636, -0.006443937309086323, -0.2636377215385437, -0.2636377215385437, -0.6474903225898743, 3.266953229904175, -0.47136712074279785, -0.006895868573337793, -0.6335548758506775, -0.9054273366928101, -0.18079182505607605, -0.8581299185752869, -1.1676706075668335, -0.5185656547546387, -0.027174508199095726, -0.027174508199095726, 0, -0.0738125741481781, -0.531063973903656, -0.05600588023662567, -0.5592953562736511, 1.7004790306091309, -0.12567274272441864, 0, -0.9135702252388, 1.028686285018921, 0, 0, -0.0929313525557518, -0.0929313525557518, -0.728891909122467, -1.1704210042953491, -0.6899932622909546, -0.05843357369303703, -0.01578238606452942, -0.11359944939613342, -1.946993350982666, -0.04214845225214958, -0.7077111005783081, -0.002102144993841648, -1.4035227298736572, 2.1129629611968994, -0.00138785969465971, -1.038822889328003, -0.8794857263565063, 0, -0.43041056394577026, -0.43041056394577026, 1.1215182542800903, -0.43041056394577026, -0.9143126010894775, -0.6444416642189026, -0.6444416642189026, -0.9143126010894775, -0.33156320452690125, 0, -0.3932662010192871, 0, 0, 0, 0, 0, 0, -0.894939661026001, -0.6431266069412231, -0.6431266069412231, 0, 0, 2.372987985610962, 0, 0, 0, 0, 0, -0.12567274272441864, 0, 0, -1.4664111137390137, -0.12567274272441864, -0.12567274272441864, 1.509617567062378, -0.49900227785110474, -0.741360068321228, 1.0996252298355103, -0.5827630758285522, -0.4519699513912201, -0.5196782946586609, -0.014208721928298473, -0.9143126010894775, 0, 0, -0.0003087272052653134, -0.9330903887748718, -0.7528202533721924, 0, 0, -0.0003087272052653134, -1.0219382047653198, -0.5397534370422363, -0.5397534370422363, 0, -0.16796092689037323, 0, 0, -0.33156320452690125, 0, -0.028841601684689522, -0.33156320452690125, -1.7713963985443115, -0.6060088276863098, -0.6060088276863098, 2.6798903942108154, -0.2789709270000458, -0.48549598455429077, -0.21484126150608063, -0.21484126150608063, -1.3165682554244995, -1.3165682554244995, -0.3277052044868469, -0.3277052044868469, 0, -0.4968721866607666, 0, 0, 0, -0.4968721866607666, -0.9330903887748718, -0.9330903887748718, 0, 0, 3.535757303237915, 2.372987985610962, 2.997424364089966, 5.110175609588623, 1.1627684831619263, 1.1627684831619263, 1.1627684831619263, 1.1627684831619263, -0.7528202533721924, -0.04169771075248718, -0.04169771075248718, -2.0926336195816115], [-0.06905234605073929, -1.069097638130188, 4.6340789794921875, -0.061119742691516876, -0.05855783820152283, 2.9035754203796387, -3.4870059490203857, -3.82804012298584, 3.413785219192505, -0.12518161535263062, -0.04308959096670151, -0.10724387317895889, -0.041283439844846725, -0.1694146990776062, 0.24777713418006897, -0.06679828464984894, -0.4807797968387604, -0.03530954197049141, -0.033829499036073685, -0.031928759068250656, -0.031928759068250656, -0.06683505326509476, -0.2091372013092041, -0.2091372013092041, -0.026921270415186882, -0.026921270415186882, -0.025792835280299187, -0.8180646300315857, -0.3314935564994812, -0.30901193618774414, -0.28236696124076843, -0.5534875988960266, -0.36105775833129883, -2.0285725593566895, -0.5217795968055725, -0.38043975830078125, -0.946911096572876, -0.5633874535560608, -0.09419257193803787, 1.6976253986358643, -0.01150778029114008, -0.030311891809105873, -1.258266568183899, -0.013050221838057041, 1.6847336292266846, -1.5581151247024536, -0.3859090209007263, -0.3141412138938904, -0.3141412138938904, 4.265204429626465, 0, -0.7499070763587952, 0, 0, 0, 0, 0, -1.7877105474472046, -0.7499070763587952, 0, -0.830638587474823, 1.0274109840393066, -0.14821600914001465, -1.4543330669403076, 1.114859700202942, -0.15967124700546265, -0.004584331996738911, -0.004584331996738911, -0.6098577380180359, -0.04056830704212189, -0.6501895189285278, 0, -0.022501641884446144, -2.365827798843384, -0.15905478596687317, -0.15905478596687317, 0, 0.7830743789672852, -0.022501641884446144, -0.022501641884446144, 0, 0, -0.6648352742195129, -0.9449368119239807, -0.8794474005699158, -0.40443968772888184, 0, 0, -0.4185822606086731, -0.015683026984333992, -0.971172034740448, 0, 0, -0.8155814409255981, -0.010959722101688385, 2.621633768081665, -0.4185822606086731, 0, -0.33408641815185547, -0.33408641815185547, -0.8848597407341003, -0.01322229951620102, -1.2272604703903198, -0.05838913097977638, -0.05838913097977638, -1.2272604703903198, -0.38027215003967285, 0, -0.5583531260490417, -0.0978802964091301, 0, 0, 0, 0, 0, -0.5716460347175598, 2.2472710609436035, 2.2472710609436035, -2.537341594696045, 0, -0.7631525993347168, 0, 0, 0, 0, -0.0018509560031816363, -0.0018509560031816363, -0.0018509560031816363, 0, -1.4805485010147095, 0, 0, -0.41969332098960876, -0.19599732756614685, -0.9043993949890137, -0.05216966196894646, -0.15384618937969208, -0.266659140586853, -0.5185553431510925, -0.04056830704212189, -1.2272604703903198, 0, 0, 0, -0.569824755191803, 2.2636170387268066, -0.3081620931625366, -0.3081620931625366, 0, -0.4897858202457428, -0.4306776821613312, -0.4306776821613312, 0, -0.0858096107840538, 0, 0, -0.38027215003967285, 0, -0.40443968772888184, -0.38027215003967285, 5.273058891296387, -0.605863630771637, -0.605863630771637, -0.25861161947250366, -0.02983742579817772, -0.1326114684343338, -0.045588456094264984, -0.045588456094264984, -0.20241020619869232, -0.20241020619869232, 0, 0, 0, -0.46334126591682434, 0, -0.005900053307414055, -0.005900053307414055, -0.46334126591682434, -0.20241020619869232, -0.20241020619869232, 0, 0, -0.8155814409255981, -0.7631525993347168, -0.21682807803153992, -1.008089542388916, -0.05216966196894646, -0.05216966196894646, -0.05216966196894646, -0.05216966196894646, 2.631042003631592, 5.002101421356201, 5.002101421356201, -3.634062281856136]]}}}}}, "intentDomains": {}, "extraSentences": [["vi", "<PERSON><PERSON> ch<PERSON>o"], ["vi", "<PERSON><PERSON><PERSON> b<PERSON>n"], ["vi", "Hello"], ["vi", "Hi"], ["vi", "Bạn có ở đó không?"], ["vi", "Chào VanLangBot"], ["vi", "chao"], ["vi", "alo"], ["vi", "a lô"], ["vi", "ê bot"], ["vi", "<PERSON><PERSON><PERSON>"], ["vi", "Bye"], ["vi", "Goodbye"], ["vi", "<PERSON><PERSON><PERSON> tạm bi<PERSON>"], ["vi", "Hẹn gặp lại"], ["vi", "<PERSON><PERSON><PERSON> bot"], ["vi", "c<PERSON>m <PERSON>n"], ["vi", "thank you"], ["vi", "thanks"], ["vi", "Bạn là ai"], ["vi", "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u về bạn"], ["vi", "Bạn tên gì"], ["vi", "Bạn là gì"], ["vi", "Thông tin về bạn"], ["vi", "VanLangBot là ai"], ["vi", "<PERSON>ạn làm đ<PERSON> gì"], ["vi", "Bạn gi<PERSON><PERSON> gì đ<PERSON><PERSON> tôi"], ["vi", "<PERSON><PERSON><PERSON> n<PERSON>ng c<PERSON><PERSON> bạn"], ["vi", "<PERSON><PERSON><PERSON> năng của bạn là gì"], ["vi", "<PERSON><PERSON> cho tôi nghe bạn biết làm gì"], ["vi", "<PERSON><PERSON>y g<PERSON> r<PERSON>i"], ["vi", "<PERSON><PERSON><PERSON> nay ngày m<PERSON>y"], ["vi", "Bây giờ là mấy giờ"], ["vi", "<PERSON><PERSON><PERSON><PERSON> gian hiện tại"], ["vi", "<PERSON><PERSON><PERSON> tháng năm nay"], ["vi", "Chi tiêu của tôi"], ["vi", "Tổng chi tiêu của tôi là bao nhiêu"], ["vi", "<PERSON><PERSON> tổng chi tiêu"], ["vi", "<PERSON><PERSON><PERSON> hình chi tiêu"], ["vi", "Tôi đã tiêu bao nhiêu"], ["vi", "<PERSON><PERSON><PERSON><PERSON> này tiêu bao nhiêu rồi"], ["vi", "Báo cáo chi tiêu"], ["vi", "<PERSON><PERSON><PERSON><PERSON> kê chi tiêu"], ["vi", "<PERSON>em chi tiết chi tiêu"], ["vi", "Liệ<PERSON> kê các k<PERSON>n chi"], ["vi", "Tôi đã tiêu vào những gì"], ["vi", "<PERSON> tiêu cụ thể"], ["vi", "Cho tôi xem chi tiết các giao dịch chi tiêu"], ["vi", "<PERSON><PERSON><PERSON>n chi gần đây"], ["vi", "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> c<PERSON>a tôi"], ["vi", "<PERSON><PERSON>ng thu nhập là bao nhiêu"], ["vi", "<PERSON><PERSON> tổng thu nhập"], ["vi", "<PERSON><PERSON><PERSON> hình thu nhập"], ["vi", "<PERSON><PERSON><PERSON> ki<PERSON>m đ<PERSON><PERSON><PERSON> bao n<PERSON>u"], ["vi", "<PERSON><PERSON><PERSON><PERSON> này thu nhập bao nhiêu"], ["vi", "<PERSON><PERSON><PERSON> c<PERSON>o thu nhập"], ["vi", "Tổng thu nhập thì sao"], ["vi", "<PERSON>em chi tiết thu nhập"], ["vi", "<PERSON><PERSON><PERSON> kê các k<PERSON>n thu"], ["vi", "<PERSON><PERSON><PERSON> nguồn thu của tôi"], ["vi", "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> cụ thể"], ["vi", "Cho tôi xem chi tiết các giao dịch thu nhập"], ["vi", "<PERSON><PERSON><PERSON>n thu gần đây"], ["vi", "<PERSON><PERSON><PERSON><PERSON> vay của tôi"], ["vi", "Tổng nợ là bao nhiêu"], ["vi", "<PERSON><PERSON> vay"], ["vi", "<PERSON><PERSON><PERSON> hình nợ"], ["vi", "<PERSON><PERSON><PERSON> đang nợ bao nhiêu"], ["vi", "<PERSON><PERSON><PERSON> c<PERSON>c k<PERSON>n vay hiện tại"], ["vi", "Báo c<PERSON>o <PERSON> vay"], ["vi", "<PERSON> tiết k<PERSON> vay"], ["vi", "Liệt kê các k<PERSON>n nợ"], ["vi", "<PERSON><PERSON><PERSON><PERSON> vay abc chi tiết"], ["vi", "Thông tin cụ thể về khoản vay"], ["vi", "Số dư hiện tại là bao nhiêu"], ["vi", "Số dư của tôi"], ["vi", "<PERSON><PERSON><PERSON>n còn bao nhiêu tiền"], ["vi", "<PERSON><PERSON><PERSON> tra số dư"], ["vi", "<PERSON><PERSON><PERSON> còn lại bao nhi<PERSON>u"], ["vi", "Balance"], ["vi", "<PERSON><PERSON> sách của tôi còn bao nhiêu?"], ["vi", "<PERSON><PERSON><PERSON> tra ngân sách tháng này"], ["vi", "<PERSON><PERSON><PERSON> hình ngân s<PERSON>ch"], ["vi", "Tôi đã dùng bao nhiêu ngân sách"], ["vi", "<PERSON><PERSON><PERSON> mức chi tiêu còn lại"], ["vi", "<PERSON><PERSON><PERSON>n đầu tư của tôi"], ["vi", "<PERSON><PERSON><PERSON> hình đầu tư"], ["vi", "<PERSON><PERSON> danh mục đ<PERSON>u tư"], ["vi", "<PERSON><PERSON><PERSON> đang đầu tư gì"], ["vi", "<PERSON><PERSON>m sao để tiết kiệm tiền?"], ["vi", "Mẹo tiết kiệm tiền"], ["vi", "<PERSON><PERSON><PERSON> muốn tiết kiệm nhi<PERSON>u hơn"], ["vi", "<PERSON><PERSON><PERSON> cách tiết kiệm"], ["vi", "<PERSON><PERSON><PERSON><PERSON><PERSON> tiế<PERSON> ki<PERSON>m"], ["vi", "<PERSON><PERSON><PERSON><PERSON> tiết hôm nay thế nào"], ["vi", "<PERSON><PERSON> chuy<PERSON>n cư<PERSON>i đi"], ["vi", "Bạn thích ăn gì"], ["vi", "1 cộng 1 bằng mấy"], ["vi", "abc xyz"], ["vi", "<PERSON><PERSON><PERSON> vừa nhận l<PERSON>"], ["vi", "<PERSON><PERSON><PERSON><PERSON> thu nhập mới"], ["vi", "<PERSON><PERSON> nhận tiền thưởng vào ứng dụng"], ["vi", "<PERSON><PERSON><PERSON> có một khoản thu mới"], ["vi", "Tôi đang nợ bao nhiêu?"], ["vi", "<PERSON>ó khoản vay nào chưa trả không?"], ["vi", "Tôi còn bao nhiêu nợ?"], ["vi", "<PERSON><PERSON><PERSON> tra nợ của tôi với"], ["vi", "Cho tôi xem báo cáo chi tiêu"], ["vi", "<PERSON><PERSON><PERSON> muốn xem thống kê tài ch<PERSON>h"], ["vi", "Có thể cho tôi biểu đồ thu chi tháng này không?"], ["vi", "<PERSON>em báo cáo theo tuần đ<PERSON><PERSON><PERSON> không?"], ["vi", "Danh mục chi tiêu hiện có là gì?"], ["vi", "Tôi có thể tạo danh mục mới không?"], ["vi", "Liệ<PERSON> kê các danh mục thu chi"], ["vi", "T<PERSON>i muốn phân loại chi tiêu"], ["vi", "<PERSON><PERSON>m sao bật thông báo chi tiêu?"], ["vi", "<PERSON><PERSON><PERSON> mu<PERSON><PERSON><PERSON><PERSON> n<PERSON> khi sắp hết ngân sách"], ["vi", "<PERSON><PERSON><PERSON> c<PERSON>nh b<PERSON>o tài ch<PERSON>h"], ["vi", "Thông báo quá giới hạn ngân sách có hoạt động không?"], ["vi", "Đặt ngân sách tháng này như thế nào?"], ["vi", "T<PERSON>i muốn giới hạn chi tiêu"], ["vi", "<PERSON><PERSON><PERSON><PERSON> lập ngân sách mới"], ["vi", "<PERSON><PERSON><PERSON> c<PERSON>n lập kế hoạch chi tiêu tháng tới"], ["vi", "Tôi chưa đăng nhập thì chatbot có hoạt động không?"], ["vi", "<PERSON>ó cần đăng nhập để sử dụng bot không?"], ["vi", "<PERSON>t yêu cầu tài khoản đúng không?"], ["vi", "Tôi không đăng nhập mà vẫn hỏi được à?"], ["vi", "<PERSON>t này làm đ<PERSON> gì?"], ["vi", "<PERSON><PERSON><PERSON> năng của VanLangBot là gì?"], ["vi", "Bot có thể giúp gì cho tôi?"], ["vi", "Tôi có thể hỏi bot về tài chính như thế nào?"], ["vi", "Tôi có thể đầu tư gì trong app?"], ["vi", "Ứng dụng hỗ trợ quản lý đầu tư không?"], ["vi", "<PERSON><PERSON> nhận lợi nhuận đầu tư ở đâu?"], ["vi", "<PERSON> dõi đầu tư crypto như thế nào?"], ["vi", "T<PERSON><PERSON> muốn tiết kiệm tiền mua xe"], ["vi", "Đặt mục tiêu tiết kiệm ở đâu?"], ["vi", "<PERSON>àm sao để tạo kế hoạch tiết kiệm?"], ["vi", "Có thể theo dõi quá trình tiết kiệm không?"], ["vi", "<PERSON><PERSON><PERSON> muốn nh<PERSON>c ghi thu nhập mỗi tháng"], ["vi", "<PERSON>àm sao tạo nhắc định kỳ?"], ["vi", "<PERSON><PERSON><PERSON><PERSON> tôi đóng tiền trọ hàng tháng nhé"], ["vi", "Ứng dụng có hỗ trợ nhắc định kỳ không?"], ["vi", "Dữ liệu của tôi có an toàn không?"], ["vi", "Ứng dụng có mã hóa thông tin không?"], ["vi", "Tôi có thể xoá tài khoản không?"], ["vi", "VanLangBot có chia sẻ dữ liệu với bên thứ ba không?"], ["vi", "Bạn có yêu tôi không?"], ["vi", "<PERSON>t có buồn không khi tôi tiêu nhiều tiền?"], ["vi", "Tôi phá sản rồi, bạn gi<PERSON><PERSON> gì đư<PERSON>?"], ["vi", "Bạn có thể kể chuyện cười không?"]]}, "ner": {"settings": {"tag": "ner", "entityPreffix": "%", "entitySuffix": "%"}, "rules": {}}, "nlgManager": {"settings": {"tag": "nlg-manager"}, "responses": {"vi": {"greeting.hello": [{"answer": "Chào bạn! Tôi là VanLangBot – trợ lý tài chính của bạn."}, {"answer": "Xin chào! Tôi có thể giúp gì cho bạn trong việc quản lý tài chính?"}], "greeting.farewell": [{"answer": "Tạm biệt bạn! Chúc bạn một ngày tốt lành!"}, {"answer": "<PERSON><PERSON>t vui được hỗ trợ bạn. Hẹn gặp lại!"}], "bot.introduction": [{"answer": "<PERSON><PERSON><PERSON> <PERSON>, trợ lý tài ch<PERSON>h AI đư<PERSON><PERSON> thiết kế để giúp bạn quản lý tài chính cá nhân hiệu quả."}], "bot.capabilities": [{"answer": "<PERSON><PERSON><PERSON> có thể gi<PERSON>p bạn theo dõi thu nhập, chi ti<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> vay, đ<PERSON><PERSON> tư, phân tích tài chính và đưa ra các gợi ý hữu ích."}], "common.time_date": [{"answer": "<PERSON><PERSON> tôi xem thời gian hiện tại cho bạn."}], "expense.query_summary": [{"answer": "<PERSON><PERSON> tôi kiểm tra tổng quan chi tiêu của bạn."}], "expense.query_detail": [{"answer": "<PERSON><PERSON> tôi liệt kê chi tiết các khoản chi tiêu của bạn."}], "income.query_summary": [{"answer": "<PERSON><PERSON> tôi kiểm tra tổng quan thu nhập củ<PERSON> bạn."}], "income.query_detail": [{"answer": "<PERSON><PERSON> tôi liệt kê chi tiết các kho<PERSON>n thu nhập của bạn."}], "loan.query_summary": [{"answer": "<PERSON><PERSON> tôi kiểm tra các kho<PERSON>n vay của bạn."}], "loan.query_detail": [{"answer": "<PERSON><PERSON> tôi cung cấp chi tiết về khoản vay của bạn."}], "balance.query": [{"answer": "<PERSON><PERSON> tôi kiểm tra số dư tài khoản của bạn."}], "budget.check": [{"answer": "<PERSON><PERSON> tôi kiểm tra tình hình ngân sách của bạn."}], "investment.query": [{"answer": "<PERSON><PERSON> tôi xem thông tin đầu tư của bạn."}], "saving.advice": [{"answer": "Tiết kiệm là một thói quen tốt! Bạn có thể bắt đầu bằng việc đặt mục tiêu và theo dõi chi tiêu."}], "nlu.fallback": [{"answer": "<PERSON><PERSON> lỗi, tôi chưa hiểu rõ câu hỏi của bạn. Tôi chỉ có thể hỗ trợ các vấn đề liên quan đến tài ch<PERSON>h."}, {"answer": "Tôi không được huấn luyện để trả lời câu hỏi này. Bạn có câu hỏi nào khác về tài chính không?"}], "income.add": [{"answer": "<PERSON>ạn có thể thêm thu nhập bằng cách nhấn vào 'Thêm thu' và nhập số tiền, mô tả, ng<PERSON><PERSON> nhận."}, {"answer": "<PERSON><PERSON><PERSON> m<PERSON> '<PERSON><PERSON>h<PERSON>', chọ<PERSON> '<PERSON>h<PERSON><PERSON>' và điền đầy đủ thông tin để ghi lại khoản thu mới nhé!"}], "loan.check": [{"answer": "Bạn đang còn {loanAmount} chưa thanh toán. <PERSON><PERSON><PERSON> lên kế hoạch trả nợ sớm nhé!"}, {"answer": "<PERSON><PERSON><PERSON> tạ<PERSON>, bạ<PERSON> có {loanCount} k<PERSON><PERSON><PERSON> vay, tổng nợ là {loanAmount}."}], "report.view": [{"answer": "<PERSON>ạn có thể vào mục 'Báo cáo' để xem biểu đồ thu chi theo tuần, tháng hoặc năm."}, {"answer": "<PERSON><PERSON> liệu thống kê đư<PERSON>c tổng hợp theo danh mục và thời gian – bạn hãy chọn khoảng thời gian cần xem nhé!"}], "category.list": [{"answer": "Bạn có thể tạo và chỉnh sửa danh mục ở phần 'Cài đặt danh mục'."}, {"answer": "<PERSON><PERSON><PERSON> c<PERSON> các danh mục n<PERSON> Ăn u<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, v.v."}], "notification.settings": [{"answer": "Bạn có thể bật/tắt thông báo trong mục 'Cài đặt > Thông báo'."}, {"answer": "Ứng dụng sẽ gửi cảnh báo khi bạn vượt quá 80% ngân sách hoặc có khoản chi lớn bất thường."}], "budget.set": [{"answer": "Bạn vào phần '<PERSON><PERSON> sách', chọn '<PERSON><PERSON><PERSON> mới' và nhập số tiền giới hạn cho từng danh mục."}, {"answer": "<PERSON><PERSON> thiết lậ<PERSON> ng<PERSON>, h<PERSON><PERSON> chọ<PERSON> k<PERSON><PERSON> thời gian và danh mụ<PERSON>, sau đ<PERSON> nhập số tiền dự kiến chi."}], "auth.require": [{"answer": "Bạn cần đăng nhập để sử dụng đầy đủ tính năng của VanLangBot và bảo vệ dữ liệu cá nhân."}, {"answer": "<PERSON><PERSON> tương tác tốt nhất và truy xuất dữ liệu ch<PERSON>h xác, vui lòng đăng nhập tài khoản trước nhé!"}], "chatbot.scope": [{"answer": "VanLangBot có thể giúp bạn quản lý chi tiêu, thu <PERSON><PERSON><PERSON><PERSON>, ng<PERSON>, k<PERSON><PERSON><PERSON> vay và cả đầu tư đơn giản."}, {"answer": "Tôi hỗ trợ bạn ghi lại giao d<PERSON>, xem báo c<PERSON><PERSON> tà<PERSON>, tư vấn tiết kiệm và trả lời các câu hỏi liên quan đến ứng dụng VanLang Budget."}], "investment.support": [{"answer": "<PERSON>ạn có thể thêm khoản đầu tư ở mục 'Đầu tư', nhập số tiền và loại tài sản nh<PERSON> crypto, cổ phiếu, hoặc khác."}, {"answer": "VanLangBot hỗ trợ ghi nhận lãi/lỗ và theo dõi biến động tài sản đơn giản để bạn quản lý đầu tư hiệu quả hơn."}], "saving.goal": [{"answer": "<PERSON><PERSON><PERSON> có thể tạo mục tiêu tiết kiệm tại phần '<PERSON><PERSON> hoạch tài ch<PERSON>', nh<PERSON><PERSON> tên mục tiêu, số tiền và thời hạn hoàn thành."}, {"answer": "Ứng dụng sẽ nhắc nhở bạn tiến độ và đưa gợi ý điều chỉnh nếu bạn chi vượt mức kế hoạch."}], "reminder.setup": [{"answer": "B<PERSON>n có thể tạo giao dịch định kỳ trong mục '<PERSON><PERSON><PERSON> lịch', chọn ngày lặp lại và nội dung cần nhắc."}, {"answer": "Mỗi tháng ứng dụng sẽ tự động ghi nhận giao dịch nếu bạn bật chế độ định kỳ nhé!"}], "security.privacy": [{"answer": "<PERSON><PERSON>g tôi sử dụng các phương pháp mã hóa hiện đại để bảo vệ dữ liệu người dùng."}, {"answer": "Bạn hoàn toàn có quyền xoá tài khoản và dữ liệu bất kỳ lúc nào tại phần 'Cài đặt'."}, {"answer": "VanLangBot cam kết không chia sẻ dữ liệu cá nhân với bất kỳ bên thứ ba nào."}], "funny.chat": [{"answer": "Tô<PERSON> luôn yêu bạn và túi tiền của bạn 😄"}, {"answer": "Tiêu nhiều thì phải tiết kiệm lại, tôi luôn bên bạn!"}, {"answer": "<PERSON><PERSON> sản chỉ là bước đệm để làm lại từ đầu – tôi sẽ giúp bạn lên kế hoạch!"}, {"answer": "Một đồng tiết kiệm là một đồng... không tiêu, haha!"}]}}}, "actionManager": {"settings": {"tag": "action-manager"}, "actions": {}}, "slotManager": {}}