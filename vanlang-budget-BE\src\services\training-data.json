[{"intent": "greeting.hello", "language": "vi", "utterances": ["<PERSON><PERSON> ch<PERSON>o", "<PERSON><PERSON><PERSON> b<PERSON>n", "Hello", "Hi", "Bạn có ở đó không?", "Chào VanLangBot", "chao", "alo", "a lô", "ê bot"], "answers": ["Chào bạn! Tôi là VanLangBot – trợ lý tài chính của bạn.", "Xin chào! Tôi có thể giúp gì cho bạn trong việc quản lý tài chính?"]}, {"intent": "greeting.farewell", "language": "vi", "utterances": ["<PERSON><PERSON><PERSON>", "Bye", "Goodbye", "<PERSON><PERSON><PERSON> tạm bi<PERSON>", "Hẹn gặp lại", "<PERSON><PERSON><PERSON> bot", "c<PERSON>m <PERSON>n", "thank you", "thanks"], "answers": ["Tạm biệt bạn! Chúc bạn một ngày tốt lành!", "<PERSON><PERSON>t vui được hỗ trợ bạn. Hẹn gặp lại!"]}, {"intent": "bot.introduction", "language": "vi", "utterances": ["Bạn là ai", "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u về bạn", "Bạn tên gì", "Bạn là gì", "Thông tin về bạn", "VanLangBot là ai"], "answers": ["<PERSON><PERSON><PERSON> <PERSON>, trợ lý tài ch<PERSON>h AI đư<PERSON><PERSON> thiết kế để giúp bạn quản lý tài chính cá nhân hiệu quả."]}, {"intent": "bot.capabilities", "language": "vi", "utterances": ["<PERSON>ạn làm đ<PERSON> gì", "Bạn gi<PERSON><PERSON> gì đ<PERSON><PERSON> tôi", "<PERSON><PERSON><PERSON> n<PERSON>ng c<PERSON><PERSON> bạn", "<PERSON><PERSON><PERSON> năng của bạn là gì", "<PERSON><PERSON> cho tôi nghe bạn biết làm gì"], "answers": ["<PERSON><PERSON><PERSON> có thể gi<PERSON>p bạn theo dõi thu nhập, chi ti<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> vay, đ<PERSON><PERSON> tư, phân tích tài chính và đưa ra các gợi ý hữu ích."]}, {"intent": "common.time_date", "language": "vi", "utterances": ["<PERSON><PERSON>y g<PERSON> r<PERSON>i", "<PERSON><PERSON><PERSON> nay ngày m<PERSON>y", "Bây giờ là mấy giờ", "<PERSON><PERSON><PERSON><PERSON> gian hiện tại", "<PERSON><PERSON><PERSON> tháng năm nay"], "answers": ["<PERSON><PERSON> tôi xem thời gian hiện tại cho bạn."]}, {"intent": "expense.query_summary", "language": "vi", "utterances": ["Chi tiêu của tôi", "Tổng chi tiêu của tôi là bao nhiêu", "<PERSON><PERSON> tổng chi tiêu", "<PERSON><PERSON><PERSON> hình chi tiêu", "Tôi đã tiêu bao nhiêu", "<PERSON><PERSON><PERSON><PERSON> này tiêu bao nhiêu rồi", "Báo cáo chi tiêu", "<PERSON><PERSON><PERSON><PERSON> kê chi tiêu"], "answers": ["<PERSON><PERSON> tôi kiểm tra tổng quan chi tiêu của bạn."]}, {"intent": "expense.query_detail", "language": "vi", "utterances": ["<PERSON>em chi tiết chi tiêu", "Liệ<PERSON> kê các k<PERSON>n chi", "Tôi đã tiêu vào những gì", "<PERSON> tiêu cụ thể", "Cho tôi xem chi tiết các giao dịch chi tiêu", "<PERSON><PERSON><PERSON>n chi gần đây"], "answers": ["<PERSON><PERSON> tôi liệt kê chi tiết các khoản chi tiêu của bạn."]}, {"intent": "income.query_summary", "language": "vi", "utterances": ["<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> c<PERSON>a tôi", "<PERSON><PERSON>ng thu nhập là bao nhiêu", "<PERSON><PERSON> tổng thu nhập", "<PERSON><PERSON><PERSON> hình thu nhập", "<PERSON><PERSON><PERSON> ki<PERSON>m đ<PERSON><PERSON><PERSON> bao n<PERSON>u", "<PERSON><PERSON><PERSON><PERSON> này thu nhập bao nhiêu", "<PERSON><PERSON><PERSON> c<PERSON>o thu nhập", "Tổng thu nhập thì sao"], "answers": ["<PERSON><PERSON> tôi kiểm tra tổng quan thu nhập củ<PERSON> bạn."]}, {"intent": "income.query_detail", "language": "vi", "utterances": ["<PERSON>em chi tiết thu nhập", "<PERSON><PERSON><PERSON> kê các k<PERSON>n thu", "<PERSON><PERSON><PERSON> nguồn thu của tôi", "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> cụ thể", "Cho tôi xem chi tiết các giao dịch thu nhập", "<PERSON><PERSON><PERSON>n thu gần đây"], "answers": ["<PERSON><PERSON> tôi liệt kê chi tiết các kho<PERSON>n thu nhập của bạn."]}, {"intent": "loan.query_summary", "language": "vi", "utterances": ["<PERSON><PERSON><PERSON><PERSON> vay của tôi", "Tổng nợ là bao nhiêu", "<PERSON><PERSON> vay", "<PERSON><PERSON><PERSON> hình nợ", "<PERSON><PERSON><PERSON> đang nợ bao nhiêu", "<PERSON><PERSON><PERSON> c<PERSON>c k<PERSON>n vay hiện tại", "Báo c<PERSON>o <PERSON> vay"], "answers": ["<PERSON><PERSON> tôi kiểm tra các kho<PERSON>n vay của bạn."]}, {"intent": "loan.query_detail", "language": "vi", "utterances": ["<PERSON> tiết k<PERSON> vay", "Liệt kê các k<PERSON>n nợ", "<PERSON><PERSON><PERSON><PERSON> vay abc chi tiết", "Thông tin cụ thể về khoản vay"], "answers": ["<PERSON><PERSON> tôi cung cấp chi tiết về khoản vay của bạn."]}, {"intent": "balance.query", "language": "vi", "utterances": ["Số dư hiện tại là bao nhiêu", "Số dư của tôi", "<PERSON><PERSON><PERSON>n còn bao nhiêu tiền", "<PERSON><PERSON><PERSON> tra số dư", "<PERSON><PERSON><PERSON> còn lại bao nhi<PERSON>u", "Balance"], "answers": ["<PERSON><PERSON> tôi kiểm tra số dư tài khoản của bạn."]}, {"intent": "budget.check", "language": "vi", "utterances": ["<PERSON><PERSON> sách của tôi còn bao nhiêu?", "<PERSON><PERSON><PERSON> tra ngân sách tháng này", "<PERSON><PERSON><PERSON> hình ngân s<PERSON>ch", "Tôi đã dùng bao nhiêu ngân sách", "<PERSON><PERSON><PERSON> mức chi tiêu còn lại"], "answers": ["<PERSON><PERSON> tôi kiểm tra tình hình ngân sách của bạn."]}, {"intent": "investment.query", "language": "vi", "utterances": ["<PERSON><PERSON><PERSON>n đầu tư của tôi", "<PERSON><PERSON><PERSON> hình đầu tư", "<PERSON><PERSON> danh mục đ<PERSON>u tư", "<PERSON><PERSON><PERSON> đang đầu tư gì"], "answers": ["<PERSON><PERSON> tôi xem thông tin đầu tư của bạn."]}, {"intent": "saving.advice", "language": "vi", "utterances": ["<PERSON><PERSON>m sao để tiết kiệm tiền?", "Mẹo tiết kiệm tiền", "<PERSON><PERSON><PERSON> muốn tiết kiệm nhi<PERSON>u hơn", "<PERSON><PERSON><PERSON> cách tiết kiệm", "<PERSON><PERSON><PERSON><PERSON><PERSON> tiế<PERSON> ki<PERSON>m"], "answers": ["Tiết kiệm là một thói quen tốt! Bạn có thể bắt đầu bằng việc đặt mục tiêu và theo dõi chi tiêu."]}, {"intent": "nlu.fallback", "language": "vi", "utterances": ["<PERSON><PERSON><PERSON><PERSON> tiết hôm nay thế nào", "<PERSON><PERSON> chuy<PERSON>n cư<PERSON>i đi", "Bạn thích ăn gì", "1 cộng 1 bằng mấy", "abc xyz"], "answers": ["<PERSON><PERSON> lỗi, tôi chưa hiểu rõ câu hỏi của bạn. Tôi chỉ có thể hỗ trợ các vấn đề liên quan đến tài ch<PERSON>h.", "Tôi không được huấn luyện để trả lời câu hỏi này. Bạn có câu hỏi nào khác về tài chính không?"]}, {"intent": "income.add", "language": "vi", "utterances": ["<PERSON><PERSON><PERSON> vừa nhận l<PERSON>", "<PERSON><PERSON><PERSON><PERSON> thu nhập mới", "<PERSON><PERSON> nhận tiền thưởng vào ứng dụng", "<PERSON><PERSON><PERSON> có một khoản thu mới"], "answers": ["<PERSON>ạn có thể thêm thu nhập bằng cách nhấn vào 'Thêm thu' và nhập số tiền, mô tả, ng<PERSON><PERSON> nhận.", "<PERSON><PERSON><PERSON> m<PERSON> '<PERSON><PERSON>h<PERSON>', chọ<PERSON> '<PERSON>h<PERSON><PERSON>' và điền đầy đủ thông tin để ghi lại khoản thu mới nhé!"]}, {"intent": "loan.check", "language": "vi", "utterances": ["Tôi đang nợ bao nhiêu?", "<PERSON>ó khoản vay nào chưa trả không?", "Tôi còn bao nhiêu nợ?", "<PERSON><PERSON><PERSON> tra nợ của tôi với"], "answers": ["Bạn đang còn {loanAmount} chưa thanh toán. <PERSON><PERSON><PERSON> lên kế hoạch trả nợ sớm nhé!", "<PERSON><PERSON><PERSON> tạ<PERSON>, bạ<PERSON> có {loanCount} k<PERSON><PERSON><PERSON> vay, tổng nợ là {loanAmount}."]}, {"intent": "report.view", "language": "vi", "utterances": ["Cho tôi xem báo cáo chi tiêu", "<PERSON><PERSON><PERSON> muốn xem thống kê tài ch<PERSON>h", "Có thể cho tôi biểu đồ thu chi tháng này không?", "<PERSON>em báo cáo theo tuần đ<PERSON><PERSON><PERSON> không?"], "answers": ["<PERSON>ạn có thể vào mục 'Báo cáo' để xem biểu đồ thu chi theo tuần, tháng hoặc năm.", "<PERSON><PERSON> liệu thống kê đư<PERSON>c tổng hợp theo danh mục và thời gian – bạn hãy chọn khoảng thời gian cần xem nhé!"]}, {"intent": "category.list", "language": "vi", "utterances": ["Danh mục chi tiêu hiện có là gì?", "Tôi có thể tạo danh mục mới không?", "Liệ<PERSON> kê các danh mục thu chi", "T<PERSON>i muốn phân loại chi tiêu"], "answers": ["Bạn có thể tạo và chỉnh sửa danh mục ở phần 'Cài đặt danh mục'.", "<PERSON><PERSON><PERSON> c<PERSON> các danh mục n<PERSON> Ăn u<PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, v.v."]}, {"intent": "notification.settings", "language": "vi", "utterances": ["<PERSON><PERSON>m sao bật thông báo chi tiêu?", "<PERSON><PERSON><PERSON> mu<PERSON><PERSON><PERSON><PERSON> n<PERSON> khi sắp hết ngân sách", "<PERSON><PERSON><PERSON> c<PERSON>nh b<PERSON>o tài ch<PERSON>h", "Thông báo quá giới hạn ngân sách có hoạt động không?"], "answers": ["Bạn có thể bật/tắt thông báo trong mục 'Cài đặt > Thông báo'.", "Ứng dụng sẽ gửi cảnh báo khi bạn vượt quá 80% ngân sách hoặc có khoản chi lớn bất thường."]}, {"intent": "budget.set", "language": "vi", "utterances": ["Đặt ngân sách tháng này như thế nào?", "T<PERSON>i muốn giới hạn chi tiêu", "<PERSON><PERSON><PERSON><PERSON> lập ngân sách mới", "<PERSON><PERSON><PERSON> c<PERSON>n lập kế hoạch chi tiêu tháng tới"], "answers": ["Bạn vào phần '<PERSON><PERSON> sách', chọn '<PERSON><PERSON><PERSON> mới' và nhập số tiền giới hạn cho từng danh mục.", "<PERSON><PERSON> thiết lậ<PERSON> ng<PERSON>, h<PERSON><PERSON> chọ<PERSON> k<PERSON><PERSON> thời gian và danh mụ<PERSON>, sau đ<PERSON> nhập số tiền dự kiến chi."]}, {"intent": "auth.require", "language": "vi", "utterances": ["Tôi chưa đăng nhập thì chatbot có hoạt động không?", "<PERSON>ó cần đăng nhập để sử dụng bot không?", "<PERSON>t yêu cầu tài khoản đúng không?", "Tôi không đăng nhập mà vẫn hỏi được à?"], "answers": ["Bạn cần đăng nhập để sử dụng đầy đủ tính năng của VanLangBot và bảo vệ dữ liệu cá nhân.", "<PERSON><PERSON> tương tác tốt nhất và truy xuất dữ liệu ch<PERSON>h xác, vui lòng đăng nhập tài khoản trước nhé!"]}, {"intent": "chatbot.scope", "language": "vi", "utterances": ["<PERSON>t này làm đ<PERSON> gì?", "<PERSON><PERSON><PERSON> năng của VanLangBot là gì?", "Bot có thể giúp gì cho tôi?", "Tôi có thể hỏi bot về tài chính như thế nào?"], "answers": ["VanLangBot có thể giúp bạn quản lý chi tiêu, thu <PERSON><PERSON><PERSON><PERSON>, ng<PERSON>, k<PERSON><PERSON><PERSON> vay và cả đầu tư đơn giản.", "Tôi hỗ trợ bạn ghi lại giao d<PERSON>, xem báo c<PERSON><PERSON> tà<PERSON>, tư vấn tiết kiệm và trả lời các câu hỏi liên quan đến ứng dụng VanLang Budget."]}, {"intent": "investment.support", "language": "vi", "utterances": ["Tôi có thể đầu tư gì trong app?", "Ứng dụng hỗ trợ quản lý đầu tư không?", "<PERSON><PERSON> nhận lợi nhuận đầu tư ở đâu?", "<PERSON> dõi đầu tư crypto như thế nào?"], "answers": ["<PERSON>ạn có thể thêm khoản đầu tư ở mục 'Đầu tư', nhập số tiền và loại tài sản nh<PERSON> crypto, cổ phiếu, hoặc khác.", "VanLangBot hỗ trợ ghi nhận lãi/lỗ và theo dõi biến động tài sản đơn giản để bạn quản lý đầu tư hiệu quả hơn."]}, {"intent": "saving.goal", "language": "vi", "utterances": ["T<PERSON><PERSON> muốn tiết kiệm tiền mua xe", "Đặt mục tiêu tiết kiệm ở đâu?", "<PERSON>àm sao để tạo kế hoạch tiết kiệm?", "Có thể theo dõi quá trình tiết kiệm không?"], "answers": ["<PERSON><PERSON><PERSON> có thể tạo mục tiêu tiết kiệm tại phần '<PERSON><PERSON> hoạch tài ch<PERSON>', nh<PERSON><PERSON> tên mục tiêu, số tiền và thời hạn hoàn thành.", "Ứng dụng sẽ nhắc nhở bạn tiến độ và đưa gợi ý điều chỉnh nếu bạn chi vượt mức kế hoạch."]}, {"intent": "reminder.setup", "language": "vi", "utterances": ["<PERSON><PERSON><PERSON> muốn nh<PERSON>c ghi thu nhập mỗi tháng", "<PERSON>àm sao tạo nhắc định kỳ?", "<PERSON><PERSON><PERSON><PERSON> tôi đóng tiền trọ hàng tháng nhé", "Ứng dụng có hỗ trợ nhắc định kỳ không?"], "answers": ["B<PERSON>n có thể tạo giao dịch định kỳ trong mục '<PERSON><PERSON><PERSON> lịch', chọn ngày lặp lại và nội dung cần nhắc.", "Mỗi tháng ứng dụng sẽ tự động ghi nhận giao dịch nếu bạn bật chế độ định kỳ nhé!"]}, {"intent": "security.privacy", "language": "vi", "utterances": ["Dữ liệu của tôi có an toàn không?", "Ứng dụng có mã hóa thông tin không?", "Tôi có thể xoá tài khoản không?", "VanLangBot có chia sẻ dữ liệu với bên thứ ba không?"], "answers": ["<PERSON><PERSON>g tôi sử dụng các phương pháp mã hóa hiện đại để bảo vệ dữ liệu người dùng.", "Bạn hoàn toàn có quyền xoá tài khoản và dữ liệu bất kỳ lúc nào tại phần 'Cài đặt'.", "VanLangBot cam kết không chia sẻ dữ liệu cá nhân với bất kỳ bên thứ ba nào."]}, {"intent": "funny.chat", "language": "vi", "utterances": ["Bạn có yêu tôi không?", "<PERSON>t có buồn không khi tôi tiêu nhiều tiền?", "Tôi phá sản rồi, bạn gi<PERSON><PERSON> gì đư<PERSON>?", "Bạn có thể kể chuyện cười không?"], "answers": ["Tô<PERSON> luôn yêu bạn và túi tiền của bạn 😄", "Tiêu nhiều thì phải tiết kiệm lại, tôi luôn bên bạn!", "<PERSON><PERSON> sản chỉ là bước đệm để làm lại từ đầu – tôi sẽ giúp bạn lên kế hoạch!", "Một đồng tiết kiệm là một đồng... không tiêu, haha!"]}]