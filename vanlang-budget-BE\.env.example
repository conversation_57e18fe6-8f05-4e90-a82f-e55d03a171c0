# Database Configuration
MONGODB_URI=mongodb://localhost:27017/vanlang_budget
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# Gemini AI Configuration
GEMINI_API_KEY=AIzaSyCgyvcGoItpgZMF9HDlScSwmY1PqO4aGlg
GEMINI_MODEL_NAME=gemini-2.0-flash
GEMINI_TEMPERATURE=0.7
GEMINI_TOP_K=40
GEMINI_TOP_P=0.9
GEMINI_MAX_TOKENS=1024

# Redis Configuration (Optional - for enhanced caching)
REDIS_URL=redis://localhost:6379

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Server Configuration
PORT=4000

# TensorFlow Configuration (Optional)
TENSORFLOW_BACKEND=cpu

# Logging Level
LOG_LEVEL=info 