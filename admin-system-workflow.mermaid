graph TB
    %% ═══════════════════════════════════════════════════════════════════════════════
    %% ADMIN SYSTEM WORKFLOW DIAGRAM - VanLang Budget
    %% S<PERSON> đồ chi tiết hoạt động của hệ thống quản trị viên
    %% ═══════════════════════════════════════════════════════════════════════════════

    %% Authentication Flow
    subgraph AUTH["🔐 AUTHENTICATION FLOW"]
        A1["Admin Login Page<br/>/admin/login"] --> A2{"Valid Credentials?"}
        A2 -->|Yes| A3["Generate JWT Token"]
        A2 -->|No| A4["Show Error Message"]
        A3 --> A5["Set Cookie 'token'"]
        A5 --> A6["Redirect to Dashboard"]
        A4 --> A1
    end

    %% Role-based Access Control
    subgraph RBAC["🎯 ROLE-BASED ACCESS CONTROL"]
        R1["User Request"] --> R2{"Check JWT Token"}
        R2 -->|Invalid| R3["Redirect to Login"]
        R2 -->|Valid| R4{"Check User Role"}
        R4 -->|user| R5["Access Denied"]
        R4 -->|admin| R6["Admin Permissions"]
        R4 -->|superadmin| R7["SuperAdmin Permissions"]

        R6 --> R8["Can: Manage Users<br/>Edit Content<br/>View Reports"]
        R6 --> R9["Cannot: Manage Admins<br/>Approve Content<br/>Financial Transactions"]

        R7 --> R10["Full Access<br/>All Admin Functions<br/>+ Admin Management<br/>+ Content Approval"]
    end

    %% Admin Dashboard
    subgraph DASH["📊 ADMIN DASHBOARD"]
        D1["Dashboard Page<br/>/admin/dashboard"] --> D2["Load Statistics"]
        D2 --> D3["Total Users Count"]
        D2 --> D4["Active Users Count"]
        D2 --> D5["New Users (30 days)"]
        D2 --> D6["Admin Count"]
        D3 --> D7["Display Cards"]
        D4 --> D7
        D5 --> D7
        D6 --> D7
    end

    %% User Management Flow
    subgraph USER_MGMT["👥 USER MANAGEMENT"]
        U1["Users Page<br/>/admin/users"] --> U2["Load Users List"]
        U2 --> U3["Pagination & Search"]
        U3 --> U4["Display User Table"]

        U4 --> U5{"Admin Actions"}
        U5 -->|Create| U6["Create User Form"]
        U5 -->|Edit| U7["Edit User Form"]
        U5 -->|Delete| U8["Delete Confirmation"]
        U5 -->|Toggle Status| U9["Activate/Deactivate"]

        %% SuperAdmin Only Actions
        U5 -->|Promote to Admin| U10{"SuperAdmin Only"}
        U5 -->|Demote Admin| U11{"SuperAdmin Only"}
        U10 -->|Yes| U12["Promote User"]
        U10 -->|No| U13["Access Denied"]
        U11 -->|Yes| U14["Demote Admin"]
        U11 -->|No| U13

        U6 --> U15["Validate Input"]
        U7 --> U15
        U15 --> U16["Save to Database"]
        U16 --> U17["Log Activity"]
        U17 --> U18["Refresh List"]
    end

    %% Content Management Flow
    subgraph CONTENT["📝 CONTENT MANAGEMENT"]
        C1["Site Content Page<br/>/admin/site-content"] --> C2["Select Section"]
        C2 --> C3{"Content Type"}

        C3 -->|Homepage| C4["Homepage Editor"]
        C3 -->|About| C5["About Editor"]
        C3 -->|Features| C6["Features Editor"]
        C3 -->|Pricing| C7["Pricing Editor"]
        C3 -->|Contact| C8["Contact Editor"]
        C3 -->|Footer| C9["Footer Editor"]

        C4 --> C10["Language Toggle<br/>VI/EN"]
        C5 --> C10
        C6 --> C10
        C7 --> C10
        C8 --> C10
        C9 --> C10

        C10 --> C11["WYSIWYG Editor"]
        C11 --> C12["Preview Mode"]
        C12 --> C13["Save Changes"]

        C13 --> C14{"User Role"}
        C14 -->|Admin| C15["Status: pending_review"]
        C14 -->|SuperAdmin| C16["Status: published"]

        C15 --> C17["Notify SuperAdmin"]
        C16 --> C18["Update Live Content"]
        C17 --> C19["Log Activity"]
        C18 --> C19
    end

    %% Content Approval Flow (SuperAdmin Only)
    subgraph APPROVAL["✅ CONTENT APPROVAL FLOW"]
        AP1["Pending Content"] --> AP2{"SuperAdmin Review"}
        AP2 -->|Approve| AP3["Set Status: published"]
        AP2 -->|Reject| AP4["Set Status: rejected"]
        AP2 -->|Request Changes| AP5["Add Comments"]

        AP3 --> AP6["Update Live Content"]
        AP4 --> AP7["Notify Admin"]
        AP5 --> AP7

        AP6 --> AP8["Log Approval"]
        AP7 --> AP8
    end

    %% Admin Management (SuperAdmin Only)
    subgraph ADMIN_MGMT["🛡️ ADMIN MANAGEMENT"]
        AM1["Manage Admins Page<br/>/admin/manage-admins"] --> AM2{"SuperAdmin Check"}
        AM2 -->|No| AM3["Access Denied"]
        AM2 -->|Yes| AM4["Load Admin List"]

        AM4 --> AM5["Display Admin Table"]
        AM5 --> AM6{"Admin Actions"}

        AM6 -->|Create Admin| AM7["Create Admin Form"]
        AM6 -->|Edit Admin| AM8["Edit Admin Form"]
        AM6 -->|Delete Admin| AM9["Delete Confirmation"]
        AM6 -->|Toggle Status| AM10["Activate/Deactivate"]

        AM7 --> AM11["Validate Max 3 Admins"]
        AM11 -->|Valid| AM12["Create Admin Account"]
        AM11 -->|Invalid| AM13["Show Error"]

        AM12 --> AM14["Log Admin Creation"]
        AM8 --> AM14
        AM9 --> AM14
        AM10 --> AM14
    end

    %% Activity Logging System
    subgraph LOGGING["📋 ACTIVITY LOGGING"]
        L1["Admin Action"] --> L2["Log Activity Function"]
        L2 --> L3["Capture Data:<br/>- Admin ID<br/>- Action Type<br/>- Target ID<br/>- Input Data<br/>- Result<br/>- IP Address<br/>- Timestamp"]
        L3 --> L4["Save to Database"]
        L4 --> L5["Activity Logs Page<br/>/admin/activity-logs"]

        L5 --> L6{"View Permissions"}
        L6 -->|Admin| L7["Own Activities Only"]
        L6 -->|SuperAdmin| L8["All Activities"]image.png
    end

    %% API Security Layer
    subgraph API_SEC["🔒 API SECURITY"]
        AS1["API Request"] --> AS2["Rate Limiter"]
        AS2 --> AS3["JWT Verification"]
        AS3 --> AS4["Role Check"]
        AS4 --> AS5["Input Validation"]
        AS5 --> AS6["Execute Action"]
        AS6 --> AS7["Log Activity"]
        AS7 --> AS8["Return Response"]
    end

    %% Database Operations
    subgraph DB["🗄️ DATABASE OPERATIONS"]
        DB1["Admin Actions"] --> DB2{"Operation Type"}
        DB2 -->|User CRUD| DB3["Users Collection"]
        DB2 -->|Content CRUD| DB4["SiteContent Collection"]
        DB2 -->|Admin CRUD| DB5["Users Collection<br/>role: admin/superadmin"]
        DB2 -->|Activity Log| DB6["AdminActivityLogs Collection"]

        DB3 --> DB7["MongoDB Operations"]
        DB4 --> DB7
        DB5 --> DB7
        DB6 --> DB7
    end

    %% Notification System
    subgraph NOTIF["🔔 NOTIFICATION SYSTEM"]
        N1["Admin Action"] --> N2{"Notification Trigger"}
        N2 -->|Content Pending| N3["Notify SuperAdmin"]
        N2 -->|Content Approved| N4["Notify Admin"]
        N2 -->|Content Rejected| N5["Notify Admin"]
        N2 -->|User Created| N6["System Notification"]

        N3 --> N7["Toast Notification"]
        N4 --> N7
        N5 --> N7
        N6 --> N7
    end

    %% Frontend-Backend Communication
    subgraph COMM["🔄 FRONTEND-BACKEND COMMUNICATION"]
        FE1["Admin Frontend<br/>Next.js"] --> FE2["API Calls"]
        FE2 --> FE3["/api/admin/*"]
        FE3 --> BE1["Express.js Backend"]
        BE1 --> BE2["Middleware Chain"]
        BE2 --> BE3["Controller Logic"]
        BE3 --> BE4["Database Operations"]
        BE4 --> BE5["Response"]
        BE5 --> FE4["Update UI"]
    end

    %% Connections between main flows
    AUTH --> RBAC
    RBAC --> DASH
    RBAC --> USER_MGMT
    RBAC --> CONTENT
    RBAC --> ADMIN_MGMT
    
    USER_MGMT --> LOGGING
    CONTENT --> APPROVAL
    CONTENT --> LOGGING
    ADMIN_MGMT --> LOGGING
    APPROVAL --> LOGGING
    
    LOGGING --> DB
    USER_MGMT --> DB
    CONTENT --> DB
    ADMIN_MGMT --> DB
    
    CONTENT --> NOTIF
    APPROVAL --> NOTIF
    ADMIN_MGMT --> NOTIF
    
    DASH --> COMM
    USER_MGMT --> COMM
    CONTENT --> COMM
    ADMIN_MGMT --> COMM
    
    COMM --> API_SEC
    API_SEC --> DB

    %% Styling
    classDef authStyle fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef rbacStyle fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef dashStyle fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef userStyle fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef contentStyle fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef approvalStyle fill:#e0f2f1,stroke:#004d40,stroke-width:2px
    classDef adminStyle fill:#ffebee,stroke:#b71c1c,stroke-width:2px
    classDef logStyle fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef secStyle fill:#fff8e1,stroke:#ff6f00,stroke-width:2px
    classDef dbStyle fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px
    classDef notifStyle fill:#fafafa,stroke:#424242,stroke-width:2px
    classDef commStyle fill:#f9fbe7,stroke:#827717,stroke-width:2px

    class AUTH authStyle
    class RBAC rbacStyle
    class DASH dashStyle
    class USER_MGMT userStyle
    class CONTENT contentStyle
    class APPROVAL approvalStyle
    class ADMIN_MGMT adminStyle
    class LOGGING logStyle
    class API_SEC secStyle
    class DB dbStyle
    class NOTIF notifStyle
    class COMM commStyle
