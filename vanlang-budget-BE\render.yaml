services:
  - type: web
    name: vanlang-budget-be
    env: node
    buildCommand: npm install
    startCommand: npm start
    autoDeploy: true
    healthCheckPath: /health
    envVars:
      - key: PORT
        value: 1000
      - key: NODE_ENV
        value: production
      - key: MONGODB_URI
        sync: false
      - key: JWT_SECRET
        sync: false
      - key: JWT_EXPIRES_IN
        value: 7d
      - key: CORS_ORIGIN
        value: https://vanlang-budget-fe.vercel.app,https://api-vanlang-budget.vercel.app
      - key: RATE_LIMIT_WINDOW_MS
        value: 900000
      - key: RATE_LIMIT_MAX
        value: 100 