'use client'

import React, { useState, useEffect } from 'react'
import { useTranslations } from 'next-intl'
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle
} from '@/components/ui/Card'
import {
    UserIcon,
    DollarSign,
    Calendar,
    BarChart3,
    Activity,
    Shield
} from 'lucide-react'

interface StatData {
    userCount: number
    transactionCount: number
    activeUserCount: number
    todayTransactions: number
}

interface UserInfo {
    name: string;
    email: string;
    role: string;
}

export default function AdminDashboardPage() {
    const t = useTranslations()
    const [stats, setStats] = useState<StatData>({
        userCount: 0,
        transactionCount: 0,
        activeUserCount: 0,
        todayTransactions: 0
    })
    const [loading, setLoading] = useState(true)
    const [user, setUser] = useState<UserInfo | null>(null)

    useEffect(() => {
        // L<PERSON>y thông tin người dùng từ localStorage
        if (typeof window !== 'undefined') {
            const userName = localStorage.getItem('user_name') || '';
            const userEmail = localStorage.getItem('user_email') || '';
            const userRole = localStorage.getItem('user_role') || '';

            if (userEmail) {
                setUser({
                    name: userName,
                    email: userEmail,
                    role: userRole
                });
                console.log('Đã tải thông tin người dùng từ localStorage:', userName, userEmail, userRole);
            }
        }

        // Giả lập dữ liệu trong môi trường dev
        const loadMockData = () => {
            setStats({
                userCount: 1250,
                transactionCount: 18743,
                activeUserCount: 843,
                todayTransactions: 124
            })
            setLoading(false)
        }

        try {
            const fetchStats = async () => {
                try {
                    setLoading(true)
                    const response = await fetch('/api/admin/stats')

                    if (response.ok) {
                        const data = await response.json()
                        setStats(data)
                    } else {
                        // Nếu API trả về lỗi, sử dụng dữ liệu mẫu
                        loadMockData()
                    }
                } catch (error) {
                    console.error('Lỗi khi tải dữ liệu thống kê:', error)
                    loadMockData()
                } finally {
                    setLoading(false)
                }
            }

            // Thử gọi API, nếu lỗi sẽ load dữ liệu mẫu
            fetchStats()
        } catch (error) {
            console.error('Lỗi khi khởi tạo trang:', error)
            loadMockData()
        }
    }, [])

    const stats_cards = [
        {
            title: t('admin.stats.totalUsers'),
            value: stats.userCount.toLocaleString(),
            description: t('admin.stats.registeredUsers'),
            icon: UserIcon,
            color: 'bg-blue-500'
        },
        {
            title: t('admin.stats.totalTransactions'),
            value: stats.transactionCount.toLocaleString(),
            description: t('admin.stats.allTimeTransactions'),
            icon: DollarSign,
            color: 'bg-green-500'
        },
        {
            title: t('admin.stats.activeUsers'),
            value: stats.activeUserCount.toLocaleString(),
            description: t('admin.stats.last30Days'),
            icon: Activity,
            color: 'bg-yellow-500'
        },
        {
            title: t('admin.stats.todayTransactions'),
            value: stats.todayTransactions.toLocaleString(),
            description: t('admin.stats.today'),
            icon: Calendar,
            color: 'bg-purple-500'
        }
    ]

    return (
        <div className="space-y-6 p-6">
            <div>
                <h1 className="text-3xl font-bold tracking-tight">{t('admin.dashboard')}</h1>
                <p className="text-muted-foreground mt-2">
                    {t('admin.dashboardDescription')}
                </p>
            </div>

            {/* Thêm thông tin người dùng đăng nhập */}
            {user && (
                <Card className="bg-gradient-to-r from-indigo-50 to-purple-50 border-purple-100">
                    <CardHeader>
                        <div className="flex items-center space-x-2">
                            <Shield className={user.role === 'superadmin' ? 'text-red-500' : 'text-blue-500'} />
                            <CardTitle>Thông tin đăng nhập</CardTitle>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-3">
                            <div className="grid grid-cols-2 gap-1">
                                <div className="text-sm font-medium">Tên người dùng:</div>
                                <div className="text-sm">{user.name}</div>
                            </div>
                            <div className="grid grid-cols-2 gap-1">
                                <div className="text-sm font-medium">Email:</div>
                                <div className="text-sm">{user.email}</div>
                            </div>
                            <div className="grid grid-cols-2 gap-1">
                                <div className="text-sm font-medium">Vai trò:</div>
                                <div className={`text-sm font-bold ${user.role === 'superadmin' ? 'text-red-600' : 'text-blue-600'}`}>
                                    {user.role === 'superadmin' ? 'Super Admin' : 'Admin'}
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            )}

            <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                {stats_cards.map((card, index) => (
                    <Card key={index}>
                        <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
                            <CardTitle className="text-sm font-medium">
                                {card.title}
                            </CardTitle>
                            <div className={`p-2 rounded-full ${card.color}`}>
                                <card.icon className="h-4 w-4 text-white" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {loading ? (
                                    <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
                                ) : (
                                    card.value
                                )}
                            </div>
                            <p className="text-xs text-muted-foreground mt-1">
                                {card.description}
                            </p>
                        </CardContent>
                    </Card>
                ))}
            </div>

            <div className="grid gap-4 grid-cols-1 lg:grid-cols-2">
                <Card>
                    <CardHeader>
                        <CardTitle>{t('admin.recentUsers')}</CardTitle>
                        <CardDescription>
                            {t('admin.recentUsersDescription')}
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {loading ? (
                            <div className="space-y-2">
                                {[...Array(5)].map((_, i) => (
                                    <div key={i} className="flex items-center space-x-4">
                                        <div className="h-10 w-10 rounded-full bg-muted animate-pulse"></div>
                                        <div className="space-y-2">
                                            <div className="h-4 w-24 bg-muted animate-pulse rounded"></div>
                                            <div className="h-3 w-32 bg-muted animate-pulse rounded"></div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-8">
                                <p className="text-muted-foreground">
                                    {t('admin.connectBackendForData')}
                                </p>
                            </div>
                        )}
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle>{t('admin.recentTransactions')}</CardTitle>
                        <CardDescription>
                            {t('admin.recentTransactionsDescription')}
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {loading ? (
                            <div className="space-y-2">
                                {[...Array(5)].map((_, i) => (
                                    <div key={i} className="flex items-center space-x-4">
                                        <div className="h-8 w-8 rounded-full bg-muted animate-pulse"></div>
                                        <div className="space-y-2 flex-1">
                                            <div className="h-4 w-24 bg-muted animate-pulse rounded"></div>
                                            <div className="h-3 w-32 bg-muted animate-pulse rounded"></div>
                                        </div>
                                        <div className="h-4 w-16 bg-muted animate-pulse rounded"></div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <div className="text-center py-8">
                                <p className="text-muted-foreground">
                                    {t('admin.connectBackendForData')}
                                </p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </div>
    )
} 