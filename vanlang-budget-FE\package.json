{"name": "vanlang-budget-fe", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "dev:quiet": "NODE_OPTIONS=--no-warnings next dev -p 3000", "dev:with-api": "concurrently \"next dev -p 3000\" \"cd ../stock-api && python -m uvicorn mock_api:app --host 0.0.0.0 --port 8000\"", "build": "next build", "start": "next start -p 3000", "start:api": "cd ../stock-api && python -m uvicorn mock_api:app --host 0.0.0.0 --port 8000", "lint": "next lint"}, "overrides": {"@auth/core": "0.34.2"}, "dependencies": {"next": "14.1.3", "react": "^18", "react-dom": "^18", "typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "tailwindcss": "^3.3.0", "autoprefixer": "^10.0.1", "postcss": "^8"}, "devDependencies": {"eslint": "^8", "eslint-config-next": "14.1.3"}}