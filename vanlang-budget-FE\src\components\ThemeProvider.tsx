'use client'

import React, { ReactNode } from 'react'
import { Theme<PERSON>rovider as NextThemesProvider } from 'next-themes'

interface ThemeProviderProps {
    children: ReactNode
}

export function ThemeProvider({ children }: ThemeProviderProps) {
    return (
        <NextThemesProvider
            attribute="class"
            defaultTheme="light"
            enableSystem={true}
            disableTransitionOnChange
        >
            {children}
        </NextThemesProvider>
    )
} 