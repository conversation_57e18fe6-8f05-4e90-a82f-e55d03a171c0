# Cấu hình môi trường Test
NODE_ENV=test

# Server
PORT=3333

# MongoDB (sẽ được ghi đè bởi MongoDB Memory Server)
MONGO_URI=mongodb://localhost:27017/vanlang-budget-test

# JWT
JWT_SECRET=test-jwt-secret-key-for-testing-purposes-only
JWT_EXPIRE=1d
JWT_COOKIE_EXPIRE=1

# Email (giả lập cho testing)
EMAIL_HOST=smtp.mailtrap.io
EMAIL_PORT=2525
EMAIL_USER=test-user
EMAIL_PASS=test-password
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=VanLang Budget (Test)

# Logging
LOG_LEVEL=error

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100 