{"extends": ["next/core-web-vitals", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-inferrable-types": "off", "prefer-const": "off", "react-hooks/rules-of-hooks": "warn", "react-hooks/exhaustive-deps": "off", "@next/next/no-img-element": "off"}}