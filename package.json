{"name": "vanlang-budget-project", "version": "0.1.0", "private": true, "type": "module", "description": "VanLang Budget - Personal Finance Management Application", "workspaces": ["vanlang-budget-FE", "vanlang-budget-BE"], "dependencies": {"@auth/core": "0.34.2", "@formatjs/intl-localematcher": "^0.6.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.3.4", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.10", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.8", "@radix-ui/react-tooltip": "^1.2.3", "@reduxjs/toolkit": "^2.2.1", "@tanstack/react-query": "^5.75.5", "@tanstack/react-query-next-experimental": "^5.75.5", "autoprefixer": "^10.0.1", "axios": "^1.9.0", "chart.js": "^4.4.1", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cookies-next": "^4.2.1", "cors": "^2.8.5", "cors-anywhere": "^0.4.4", "date-fns": "^3.6.0", "express": "^5.1.0", "framer-motion": "^12.5.0", "js-cookie": "^3.0.5", "lucide-react": "^0.344.0", "mongoose": "^8.13.2", "negotiator": "^1.0.0", "next": "14.1.3", "node-fetch": "^3.3.2", "next-auth": "^4.24.11", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "postcss": "^8", "react": "^18", "react-chartjs-2": "^5.2.0", "react-day-picker": "^9.6.2", "react-dom": "^18", "react-hook-form": "^7.51.0", "react-hot-toast": "^2.5.2", "react-quill": "^2.0.0", "react-redux": "^9.1.0", "recharts": "^2.15.3", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.2.0", "tailwindcss": "^3.3.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "ws": "^8.18.1", "zod": "^3.22.4"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/negotiator": "^0.6.3", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "concurrently": "^9.1.2", "eslint": "^8", "eslint-config-next": "14.1.3", "npm-run-all": "^4.1.5", "sharp": "^0.33.5", "typescript": "^5"}, "scripts": {"dev": "cd vanlang-budget-FE && next dev -p 3000", "dev:quiet": "cd vanlang-budget-FE && NODE_OPTIONS=--no-warnings next dev -p 3000", "dev:with-api": "concurrently \"npm run dev\" \"npm run start:api\"", "build": "cd vanlang-budget-FE && next build", "start": "cd vanlang-budget-FE && next start -p 3000", "start:api": "cd stock-api && python -m uvicorn mock_api:app --host 0.0.0.0 --port 8000", "start:backend": "cd vanlang-budget-BE && npm run dev", "dev:full": "concurrently \"npm run dev\" \"npm run start:backend\" \"npm run start:api\"", "lint": "cd vanlang-budget-FE && next lint", "test": "cd vanlang-budget-FE && jest", "test:watch": "cd vanlang-budget-FE && jest --watch", "test:coverage": "cd vanlang-budget-FE && jest --coverage", "install:all": "npm install && cd vanlang-budget-FE && npm install && cd ../vanlang-budget-BE && npm install", "clean": "rm -rf node_modules vanlang-budget-FE/node_modules vanlang-budget-BE/node_modules vanlang-budget-FE/.next"}}